use jsonwebtoken::{decode, Decoding<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Algorithm};
use serde::{Deserialize, Serialize};
use async_trait::async_trait;
use serde_json::Value;

use crate::traits::Neuron;
use synapse_core::{ExecutionContext, Output, SynapseError};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
struct JwtClaims {
    sub: String,
    iss: String,
    aud: String,
    exp: usize,
    iat: usize,
    roles: Vec<String>,
}

#[derive(Clone)]
pub struct JwtValidate;

impl Default for JwtValidate {
    fn default() -> Self {
        Self::new()
    }
}

impl JwtValidate {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Neuron for JwtValidate {
    fn name(&self) -> &'static str {
        "jwt_validate"
    }

    fn description(&self) -> &'static str {
        "Validate JWT token and extract claims with issuer/audience verification"
    }

    fn version(&self) -> &'static str {
        "1.0.0"
    }

    fn input_schema(&self) -> Option<&'static str> {
        Some(r#"{"type":"object","properties":{"token":{"type":"string","description":"JWT token to validate"}},"required":["token"]}"#)
    }

    fn output_schema(&self) -> Option<&'static str> {
        Some(r#"{"type":"object","properties":{"valid":{"type":"boolean"},"claims":{"type":"object"},"error":{"type":"string"}},"required":["valid"]}"#)
    }

    async fn execute(&self, ctx: &mut ExecutionContext) -> Result<Output, SynapseError> {
        let token = ctx.input.data.get("token")
            .and_then(|v| v.as_str())
            .ok_or(SynapseError::InvalidInput("Missing token parameter".into()))?;

        let public_key_pem = std::env::var("JWT_PUBLIC_KEY").map_err(|_| SynapseError::InvalidInput("Missing JWT_PUBLIC_KEY".into()))?;
        let decoding_key = DecodingKey::from_rsa_pem(public_key_pem.as_bytes())
            .map_err(|e| SynapseError::InvalidInput(format!("Invalid public key: {}", e)))?;

        let issuer = std::env::var("JWT_ISSUER").unwrap_or_else(|_| "synapse-io".to_string());
        let audience = std::env::var("JWT_AUDIENCE").unwrap_or_else(|_| "api".to_string());

        let mut validation = Validation::new(Algorithm::RS256);
        validation.set_issuer(&[issuer.as_str()]);
        validation.set_audience(&[audience.as_str()]);
        validation.validate_exp = true;

        let mut output_data = serde_json::Map::new();
        output_data.insert("valid".to_string(), Value::Bool(false));

        match decode::<JwtClaims>(token, &decoding_key, &validation) {
            Ok(token_data) => {
                let claims = serde_json::to_value(&token_data.claims)
                    .map_err(|e| SynapseError::InvalidInput(format!("Failed to serialize claims: {}", e)))?;
                
                output_data.insert("valid".to_string(), Value::Bool(true));
                output_data.insert("claims".to_string(), claims);
                output_data.insert("error".to_string(), Value::Null);
            }
            Err(e) => {
                output_data.insert("error".to_string(), Value::String(e.to_string()));
                output_data.insert("claims".to_string(), Value::Null);
            }
        }

        let valid = output_data.get("valid").unwrap().as_bool().unwrap();
        let mut output = Output::default();
        output.data = Value::Object(output_data);
        output.status = if valid { 200 } else { 401 };

        Ok(output)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use synapse_core::Input;
    use std::collections::HashMap;
    use serde_json::Value;

    #[tokio::test]
    async fn test_jwt_validate_valid_token() {
        std::env::set_var("JWT_PUBLIC_KEY", std::env::var("TEST_PUBLIC_KEY").unwrap_or(r#"-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw...
-----END PUBLIC KEY-----"#.to_string()));
        std::env::set_var("JWT_ISSUER", "test-issuer");
        std::env::set_var("JWT_AUDIENCE", "test-audience");

        let neuron = JwtValidate::new();

        let input_data = [("token".to_string(), Value::String("eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...".to_string()))].iter().cloned().collect();
        let input = Input { data: input_data, ..Default::default() };
        let mut ctx = ExecutionContext::new(input);

        let result = neuron.execute(&mut ctx).await;
        if let Err(SynapseError::InvalidInput(ref e)) = &result {
            assert!(e.contains("Missing JWT_PUBLIC_KEY") || e.contains("Invalid public key"));
        }
    }

    #[tokio::test]
    async fn test_jwt_validate_invalid_token() {
        std::env::set_var("JWT_PUBLIC_KEY", std::env::var("TEST_PUBLIC_KEY").unwrap_or(r#"-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw...
-----END PUBLIC KEY-----"#.to_string()));
        std::env::set_var("JWT_ISSUER", "test-issuer");
        std::env::set_var("JWT_AUDIENCE", "test-audience");

        let neuron = JwtValidate::new();

        let input_data = [("token".to_string(), Value::String("invalid.token.here".to_string()))].iter().cloned().collect();
        let input = Input { data: input_data, ..Default::default() };
        let mut ctx = ExecutionContext::new(input);

        let result = neuron.execute(&mut ctx).await;
        if let Err(SynapseError::InvalidInput(ref e)) = &result {
            assert!(e.contains("Missing JWT_PUBLIC_KEY") || e.contains("Invalid public key") || e.contains("JWT encoding failed"));
        }
    }
}