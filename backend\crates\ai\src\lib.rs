//! AI crate for Synapse-IO: OpenAI/<PERSON>/<PERSON>rok proxies, Impulse NL-to-graph, diagnostics

pub mod impulse;
pub mod diagnostics;
pub mod optimizer;
pub mod proxies;

pub use impulse::{ImpulseEngine, process_natural_language};
pub use diagnostics::{diagnose_pathway, fix_suggestion};
pub use optimizer::{optimize_graph, OptimizationResult};
pub use proxies::{OpenAIProxy, ClaudeProxy, GrokProxy};

use synapse_core::SynapseError;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ai_imports() {
        // Basic import test for AI module
        let _impulse = ImpulseEngine::new();
        let _openai = OpenAIProxy::new("test_key".to_string());
        let _claude = ClaudeProxy::new("test_key".to_string());
        let _grok = GrokProxy::new("test_key".to_string());
    }
}