import React, { useCallback, useRef } from 'react';
import React<PERSON><PERSON>, {
  addEdge,
  Background,
  Controls,
  MiniMap,
  Node,
  Edge,
  Connection,
  EdgeChange,
  NodeChange,
  useNodesState,
  useEdgesState,
  ReactFlowProvider,
} from 'react-flow-renderer';
import '@react-flow/background/dist/style.css';
import '@react-flow/controls/dist/style.css';
import '@react-flow/minimap/dist/style.css';

import './canvas-page.css';

const initialNodes: Node[] = [
  {
    id: '1',
    type: 'input',
    data: { label: 'Input Node' },
    position: { x: 250, y: 25 },
  },
  {
    id: '2',
    data: { label: 'Neuron Node' },
    position: { x: 100, y: 125 },
  },
];

const initialEdges = [
  { id: 'e1-2', source: '1', target: '2' },
];

const nodeTypes = {
  input: (props: any) => (
    <div className="px-4 py-2 bg-blue-500 text-white rounded">
      {props.data.label}
    </div>
  ),
  neuron: (props: any) => (
    <div className="px-4 py-2 bg-green-500 text-white rounded shadow-md">
      {props.data.label}
    </div>
  ),
};

const CanvasPage: React.FC = () => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect = useCallback(
    (params: Connection | Edge) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  const onNodesChangeCallback = useCallback(
    (changes: NodeChange[]) => setNodes((nds) => nds.map((node) => {
      const change = changes.find((c) => c.id === node.id);
      if (change?.position) {
        node.position = change.position;
      }
      return node;
    })),
    [setNodes],
  );

  const onEdgesChangeCallback = useCallback(
    (changes: EdgeChange[]) => setEdges((eds) => eds.map((edge) => {
      const change = changes.find((c) => c.id === edge.id);
      if (change?.source) {
        edge.source = change.source;
      }
      if (change?.target) {
        edge.target = change.target;
      }
      return edge;
    })),
    [setEdges],
  );

  return (
    <div className="h-screen flex flex-col">
      <div className="p-4 bg-gray-100 border-b flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Synapse Studio - Canvas</h1>
        <div className="space-x-2">
          <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
            Export Pathway
          </button>
          <button className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
            Execute
          </button>
        </div>
      </div>
      
      <div className="flex-1 relative" ref={reactFlowWrapper}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChangeCallback}
          onEdgesChange={onEdgesChangeCallback}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          fitView
          minZoom={0.2}
          maxZoom={2}
          defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        >
          <Background color="#aaa" gap={16} />
          <Controls />
          <MiniMap />
        </ReactFlow>
      </div>

      <div className="p-4 bg-gray-100 border-t flex space-x-4">
        <div className="w-64 bg-white rounded-lg shadow p-4">
          <h3 className="font-semibold mb-2">Neurons</h3>
          <div className="space-y-1">
            <button className="w-full text-left px-2 py-1 bg-blue-100 rounded hover:bg-blue-200">
              OAuth2 Discord
            </button>
            <button className="w-full text-left px-2 py-1 bg-green-100 rounded hover:bg-green-200">
              PostgreSQL Query
            </button>
            <button className="w-full text-left px-2 py-1 bg-purple-100 rounded hover:bg-purple-200">
              OpenAI Proxy
            </button>
          </div>
        </div>
        
        <div className="flex-1 bg-white rounded-lg shadow p-4">
          <h3 className="font-semibold mb-2">Properties</h3>
          <div className="space-y-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Node Type</label>
              <select className="w-full p-2 border rounded">
                <option>Neuron</option>
                <option>Input</option>
                <option>Output</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Label</label>
              <input 
                type="text" 
                className="w-full p-2 border rounded" 
                defaultValue="Neuron Node"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CanvasPage;