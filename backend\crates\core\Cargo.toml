[package]
name = "synapse-core"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
async-trait = "0.1.77"
thiserror = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4", "serde"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid"] }
reqwest = { version = "0.11", features = ["json"] }
tracing = "0.1"
chrono = { version = "0.4", features = ["serde"] }
jsonwebtoken = "8.3"

[dev-dependencies]
loom = "0.7"
proptest = "1.4"