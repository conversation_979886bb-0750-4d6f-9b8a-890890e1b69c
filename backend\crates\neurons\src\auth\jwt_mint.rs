use jsonwebtoken::{encode, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use async_trait::async_trait;
use std::time::{SystemTime, UNIX_EPOCH};

use crate::traits::Neuron;
use synapse_core::{ExecutionContext, Output, SynapseError};

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
struct JwtClaims {
    sub: String,
    iss: String,
    aud: String,
    exp: usize,
    iat: usize,
    roles: Vec<String>,
}

#[derive(Clone)]
pub struct JwtMint;

impl Default for JwtMint {
    fn default() -> Self {
        Self::new()
    }
}

impl JwtMint {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Neuron for JwtMint {
    fn name(&self) -> &'static str {
        "jwt_mint"
    }

    fn description(&self) -> &'static str {
        "Mint JWT tokens with RS256 signature"
    }

    fn version(&self) -> &'static str {
        "1.0.0"
    }

    fn input_schema(&self) -> Option<&'static str> {
        Some(r#"{"type":"object","properties":{"user_id":{"type":"string"},"roles":{"type":"array","items":{"type":"string"}}},"required":["user_id"]}"#)
    }

    fn output_schema(&self) -> Option<&'static str> {
        Some(r#"{"type":"object","properties":{"token":{"type":"string"},"expires_at":{"type":"integer"},"claims":{"type":"object"}},"required":["token","expires_at"]}"#)
    }

    async fn execute(&self, ctx: &mut ExecutionContext) -> Result<Output, SynapseError> {
        let user_id = ctx.input.data.get("user_id")
            .and_then(|v| v.as_str())
            .ok_or(SynapseError::InvalidInput("Missing user_id".into()))?
            .to_string();

        let roles: Vec<String> = ctx.input.data.get("roles")
            .and_then(|v| v.as_array())
            .map(|arr| arr.iter().filter_map(|v| v.as_str().map(|s| s.to_string())).collect())
            .unwrap_or_default();

        let now = SystemTime::now().duration_since(UNIX_EPOCH)
            .map_err(|_| SynapseError::InvalidInput("System time error".into()))?;
        let iat = now.as_secs() as usize;
        let exp = iat + 3600;

        let issuer = std::env::var("JWT_ISSUER").unwrap_or_else(|_| "synapse-io".to_string());
        let audience = std::env::var("JWT_AUDIENCE").unwrap_or_else(|_| "api".to_string());

        let claims = JwtClaims {
            sub: user_id,
            iss: issuer,
            aud: audience,
            exp,
            iat,
            roles,
        };

        let private_key_pem = std::env::var("JWT_PRIVATE_KEY").map_err(|_| SynapseError::InvalidInput("Missing JWT_PRIVATE_KEY".into()))?;
        let private_key = EncodingKey::from_rsa_pem(private_key_pem.as_bytes())
            .map_err(|e| SynapseError::InvalidInput(format!("Invalid private key: {}", e)))?;

        let header = Header::new(Algorithm::RS256);

        let token = encode(&header, &claims, &private_key)
            .map_err(|e| SynapseError::InvalidInput(format!("JWT encoding failed: {}", e)))?;

        let output_data = serde_json::json!({
            "token": token,
            "expires_at": exp,
            "claims": claims
        });

        let mut output = Output::default();
        output.data = output_data;
        output.status = 200;

        Ok(output)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use synapse_core::Input;
    use std::collections::HashMap;
    use serde_json::Value;

    #[tokio::test]
    async fn test_jwt_mint_basic() {
        std::env::set_var("JWT_PRIVATE_KEY", std::env::var("TEST_PRIVATE_KEY").unwrap_or("-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7Za3+8J8z4u1p\n-----END PRIVATE KEY-----".to_string()));
        std::env::set_var("JWT_ISSUER", "test-issuer");
        std::env::set_var("JWT_AUDIENCE", "test-audience");

        let neuron = JwtMint::new();
        
        let input_data = [("user_id".to_string(), Value::String("test-user".to_string()))].iter().cloned().collect();
        let input = Input { data: input_data, ..Default::default() };
        let mut ctx = ExecutionContext::new(input);

        let result = neuron.execute(&mut ctx).await;
        if let Err(SynapseError::InvalidInput(ref e)) = &result {
            assert!(e.contains("Missing JWT_PRIVATE_KEY") || e.contains("Invalid private key"));
        }
    }
}