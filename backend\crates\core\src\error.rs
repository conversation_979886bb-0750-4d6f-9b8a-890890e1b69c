use thiserror::<PERSON><PERSON><PERSON>;
use serde::<PERSON><PERSON><PERSON>;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub enum SynapseError {
    #[error("Neuron not found: {0}")]
    NeuronNotFound(String),
    #[error("Execution timeout")]
    Timeout,
    #[error("Database error: {0}")]
    Database(String),
    #[error("HTTP error: {0}")]
    Http(String),
    #[error("JWT invalid")]
    AuthInvalid,
    #[error("AI generation failed: {0}")]
    AiGeneration(String),
    #[error("Security anomaly detected")]
    AnomalyDetected,
    #[error("Validation failed: {0}")]
    Validation(String),
    #[error("Rate limit exceeded")]
    RateLimitExceeded,
    #[error("Payment failed: {0}")]
    Payment(String),
    #[error("Export bundle too large")]
    ExportTooLarge,
    #[error("Marketplace publish error")]
    MarketplacePublish,
    #[error("Version conflict")]
    VersionConflict,
    #[error("<PERSON>t log tamper detected")]
    AuditTamper,
    #[error("Scalability pool exhausted")]
    PoolExhausted,
    #[error("WS connection dropped")]
    WsDropped,
    #[error("RLS policy violation")]
    RlsViolation,
    #[error("PGP encryption failed")]
    PgpEncrypt,
    #[error("Secrets vault access denied")]
    VaultDenied,
    #[error("Config missing: {0}")]
    ConfigMissing(String),
    #[error("Input invalid: {0}")]
    InvalidInput(String),
    #[error("Output serialization failed")]
    OutputSerialize,
    #[error("Graph cycle detected")]
    GraphCycle,
    #[error("Node execution error: {0}")]
    NodeError(String),
    #[error("Dependency not resolved: {0}")]
    Dependency(String),
    #[error("Test failure: {0}")]
    TestFailure(String),
}

impl From<sqlx::Error> for SynapseError {
    fn from(err: sqlx::Error) -> Self {
        SynapseError::Database(err.to_string())
    }
}

impl From<reqwest::Error> for SynapseError {
    fn from(err: reqwest::Error) -> Self {
        SynapseError::Http(err.to_string())
    }
}

impl SynapseError {
    pub fn is_recoverable(&self) -> bool {
        matches!(self, SynapseError::Timeout | SynapseError::RateLimitExceeded)
    }
}