# Synapse-IO Frontend (Synapse Studio) Planning

## Introduction

Synapse Studio serves as the central visual Integrated Development Environment (IDE) for Synapse-IO, enabling users to build, test, monitor, and deploy API pathways through an intuitive drag-and-drop interface. It leverages a neuroscience-inspired metaphor where the canvas represents the "brain," neurons act as "synapses" for processing data flows, and AI tools provide cognitive assistance for generation, diagnostics, and optimization. This design facilitates visual API building for beginners while scaling to enterprise-level complexity with modular, secure, and performant features.

The technical stack includes React 18 for the UI framework, TypeScript 5 for type safety, Vite for fast builds and hot module replacement, Zustand for lightweight global state management (handling pathway graphs, user sessions, and credits), and React Router for client-side routing with protected routes. Design principles emphasize responsive and mobile-first development using Tailwind CSS breakpoints, accessibility compliance with WCAG 2.1 (including ARIA attributes, keyboard navigation, and color contrast), and production readiness with strict TypeScript configurations and comprehensive testing.

## UI/UX Architecture

The UI/UX architecture centers on a modular, intuitive workflow that guides users from onboarding to deployment. High-level user journeys include:

- **Login and Onboarding**: Secure JWT-based authentication leading to a personalized dashboard showing user projects, tier, and available credits.
- **Pathway Creation**: From dashboard, create a new pathway to access the canvas editor; drag neurons from a categorized palette, connect inputs/outputs, and configure via inline forms.
- **AI Assistance**: Integrate natural language prompts via the Impulse interface to generate or optimize pathways; Diagnostics panel highlights errors with suggestions; Cognitive Optimizer sidebar provides real-time improvements.
- **Testing and Deployment**: Run simulations on the canvas, monitor execution logs in real-time, and deploy to production with versioning and rollback options.
- **Marketplace Exploration**: Browse and acquire new neurons via The Cortex, with search, previews, and seamless purchase integration.

Key flows are visualized below using Mermaid diagrams.

### User Workflow: Login to Deployment

```mermaid
sequenceDiagram
    participant U as User
    participant L as Login
    participant D as Dashboard
    participant C as Canvas
    participant A as AI Panel
    participant T as Testing
    participant Dep as Deployment
    participant M as Marketplace

    U->>L: Authenticate (JWT)
    L->>D: Redirect to Dashboard
    D->>U: Display projects/credits
    U->>D: Select New Pathway
    D->>C: Open Canvas Editor
    U->>C: Drag Neurons, Connect Edges
    C->>A: Prompt Impulse for Generation
    A->>C: Update Graph with AI Output
    U->>C: Configure Neuron Params
    C->>T: Run Test Simulation
    T->>C: Display Results/Logs
    U->>Dep: Deploy Pathway
    Dep->>U: Confirm Deployment ID
    Note over U,M: Optional: Browse Marketplace for Neurons
    U->>M: Search/Purchase Neuron
    M->>C: Download and Add to Palette
```

### Pathway Creation Workflow

```mermaid
flowchart TD
    Start([Start New Pathway]) --> Dashboard[Dashboard: Project List]
    Dashboard --> Palette[Open Neuron Palette]
    Palette --> Drag[Drag Neuron to Canvas]
    Drag --> Connect[Connect Inputs/Outputs]
    Connect --> Config[Configure Neuron JSON Params]
    Config --> AI[AI Impulse: Generate/Optimize?]
    AI -->|Yes| Generate[Backend AI Service Generates Graph]
    Generate --> Update[Update Canvas State]
    Update --> Test[Test Pathway Execution]
    Test -->|Success| Deploy[Deploy to Production]
    Test -->|Errors| Diagnostics[Diagnostics Panel: Analyze]
    Diagnostics --> Fix[User Fixes Issues]
    Fix --> Test
    Deploy --> End([Pathway Live])
    AI -->|No| Test
```

## Component Structure

The component hierarchy follows a modular structure for reusability and maintainability, with top-level pages orchestrating specialized components.

### Component Hierarchy

```mermaid
graph TB
    App[App.tsx: Root Provider, Auth Wrapper, Router]
    App --> Dashboard[Dashboard.tsx: Project List, New Button, Stats]
    App --> CanvasPage[CanvasPage.tsx: Pathway Editor Layout]
    App --> MarketplacePage[MarketplacePage.tsx: Search, Neuron Grid]
    App --> DeploymentPage[DeploymentPage.tsx: Status, Logs, Controls]

    CanvasPage --> ReactFlow[Canvas.tsx: ReactFlow Instance]
    CanvasPage --> Palette[NeuronPalette.tsx: Searchable List by Category]
    CanvasPage --> AI[AI Panel.tsx: Impulse Input, Diagnostics, Optimizer]
    CanvasPage --> Toolbar[Toolbar.tsx: Save, Export, Undo/Redo]

    ReactFlow --> Node[NeuronNode.tsx: Custom Node with Config Form]
    ReactFlow --> Edge[Custom Edge: Connection Visuals]
    Node --> Form[Inline JSON Editor for Params e.g., secret_key]
    Palette --> Category[Categories: Input, Auth, Processing, etc.]
    Category --> Item[NeuronItem: Icon, Name, Description]

    AI --> Impulse[Impulse Input: Textarea for NL Prompts]
    AI --> Diagnostics[Diagnostics Viewer: Error List with Fixes]
    AI --> Optimizer[Sidebar Suggestions: List of Optimizations]

    MarketplacePage --> SearchBar[Search Bar with Filters]
    SearchBar --> Grid[Grid of NeuronCard Components]
    NeuronCard --> BuyButton[Buy Button: Calls Cortex Service]
```

- **App.tsx**: Wraps the application in AuthProvider (JWT handling), StateProvider (Zustand store), and Router with routes like `/dashboard`, `/pathway/:id`, `/marketplace`. Includes global error boundary and theme toggle.
- **Dashboard.tsx**: Displays user projects as cards (name, last edited, status), with buttons for new pathway, import, and tier upgrade. Integrates with pathwayService for listing.
- **CanvasPage.tsx**: Main editor layout with sidebar (palette + AI panel), central react-flow canvas, and bottom toolbar for actions. Handles routing param for pathway ID.
- **NeuronPalette.tsx**: Collapsible sidebar with search input, categorized lists (e.g., Input, Auth, Processing from 74 neurons), each item draggable to canvas creating a Node with default config.
- **NeuronNode.tsx**: ReactFlow custom node; displays icon based on type, input/output ports, resizable handle, and popover form for editing config JSON (e.g., API keys, thresholds). Supports drag for repositioning.
- **AI Panel.tsx**: Multi-tab interface: Impulse tab with prompt textarea and generate button (calls aiService); Diagnostics tab showing error tree from tests; Optimizer tab with suggestion cards (accept/reject buttons updating graph).
- **MarketplacePage.tsx**: Full-page view with search bar (query + category filters), infinite scroll grid of NeuronCard (thumbnail, description, price, free/paid indicator), and modal for purchase confirmation calling cortexService with Stripe backend proxy.

## State Management & Data Flows

State is divided into global (Zustand store for pathway graph JSON, user profile/credits, active project) and local (ReactFlow's internal state for nodes/edges, component-specific like form inputs).

- **Global State**: `usePathwayStore` for graph {nodes: NeuronNode[], edges: Connection[], config: PathwayConfig}; `useUserStore` for auth token, tier, credits. Persisted via localStorage for offline resilience.
- **Local State**: Canvas uses ReactFlowProvider for viewport/selection; Palette uses local search state; AI panel manages prompt history.
- **Sync Mechanisms**: REST for CRUD (save/load/deploy via pathwayService); WebSocket for real-time (collaborative edits, live execution logs via websocketService). Offline changes queued and synced on reconnect.

### Data Flow: UI Event to Backend Sync

```mermaid
sequenceDiagram
    participant UI as UI Component e.g., Canvas
    participant S as State Store Zustand
    participant Serv as Service Layer
    participant B as Backend API
    participant WS as WebSocket

    UI->>S: Dispatch Event e.g., onConnect Edge
    S->>S: Update Graph JSON
    S->>UI: Re-render Canvas
    UI->>Serv: Call pathwayService.save(graph)
    Serv->>B: POST /pathways with JWT
    B->>Serv: Response PathwayID
    Serv->>S: Update ID in Store
    Note over UI,WS: Real-time: WS.onUpdate -> S.updateGraph
    WS->>B: Live Execution Logs
    B->>WS: Broadcast to Clients
    WS->>S: Update Monitoring Panel
    S->>UI: Refresh Logs
```

## Integrations

- **Backend Integration**:
  - `pathwayService.ts`: Axios instance with baseURL from VITE_API_URL, JWT interceptor. Methods: `createPathway(graph: GraphJSON): Promise<PathwayID>`, `savePathway(id: string, graph: GraphJSON): Promise<void>`, `loadPathway(id: string): Promise<GraphJSON>`, `deployPathway(id: string): Promise<DeploymentStatus>`.
  - `websocketService.ts`: Socket.io-client connection on app init, channels for `/pathway/:id` (edits), `/execution/:id` (logs). Handlers: `on('graphUpdate', updateStore)`, `on('error', showDiagnostics)`. Includes reconnect logic with exponential backoff.

- **AI Integration**:
  - `aiService.ts`: Proxies to backend /ai endpoint. Methods: `generatePathway(prompt: string): Promise<GraphJSON>`, `diagnosePathway(graph: GraphJSON): Promise<ErrorReport>`, `optimizePathway(graph: GraphJSON): Promise<Suggestions>`. Handles loading states and error fallbacks.

- **Marketplace Integration**:
  - `cortexService.ts`: REST calls to /cortex. Methods: `searchNeurons(query: string, filters: string[]): Promise<NeuronList>`, `purchase(neuron_id: string): Promise<DownloadURL>`. Integrates Stripe via backend for payments, downloads ZIP with neuron configs for palette import.

- **Security**: JWT stored in httpOnly cookie (avoid localStorage exposure), axios headers with Authorization, input sanitization via DOMPurify for user prompts/configs, rate limiting on AI calls based on credits.

## File-by-File Plans

- **/frontend/package.json**: Dependencies include `react@^18`, `react-dom@^18`, `@types/react@^18`, `typescript@^5`, `vite@^5`, `react-router-dom@^6`, `zustand@^4`, `react-flow@^11`, `antd@^5`, `tailwindcss@^3`, `axios@^1`, `socket.io-client@^4`, `vitest@^1`, `@testing-library/react@^14`, `@testing-library/jest-dom@^6`. Dev deps: `@vitejs/plugin-react`, `tsconfig-paths`. Scripts: `dev`, `build`, `test`, `lint`.
- **tsconfig.json**: `"strict": true`, `"noImplicitAny": true`, paths: `"@components/*": ["src/components/*"]`, include src and tests, jsx: react-jsx.
- **src/App.tsx**: Imports providers, sets up Router with routes (e.g., `<Route path="/pathway/:id" element={<Protected><CanvasPage /></Protected>} />`), global styles import.
- **src/components/Canvas.tsx**: `<ReactFlow nodes={nodes} edges={edges} onConnect={handleConnect} onNodesChange={handleNodesChange} fitView />`, custom NodeTypes={{neuron: NeuronNode}}, exportGraph as JSON blob.
- **src/services/pathwayService.ts**: `class PathwayService { private api = axios.create({baseURL: import.meta.env.VITE_API_URL}); constructor() { this.api.interceptors.request.use(config => { config.headers.Authorization = `Bearer ${getToken()}`; return config; }); } async savePathway(id: string, graph: GraphJSON) { return this.api.put(`/pathways/${id}`, graph); } }`, export instance.
- **src/pages/Marketplace.tsx**: `<SearchBar onSearch={handleSearch} /> <div className="grid grid-cols-1 md:grid-cols-3 gap-4"> {neurons.map(n => <NeuronCard key={n.id} neuron={n} onPurchase={handlePurchase} />)} </div>`, uses cortexService.search.
- **tests/components/Canvas.test.tsx**: `import { render, screen, fireEvent } from '@testing-library/react'; test('handles node drag', () => { render(<Canvas />); const node = screen.getByTestId('neuron-node'); fireEvent.drag(node, {clientX: 100, clientY: 100}); expect(store.getState().nodes[0].position).toEqual({x: 100, y: 100}); });`, aim for 90% coverage with mocks for services.
- Similar patterns for other files: `NeuronPalette.test.tsx` (search filter), `AI Panel.test.tsx` (prompt submission), `NeuronNode.test.tsx` (config edit), ensuring no console errors, full prop coverage.

## Performance & Accessibility

- **Performance Optimizations**: Use `React.memo` for NeuronNode to prevent unnecessary re-renders; virtualized lists (react-window) for large palettes; lazy loading with React.lazy/Suspense for pages like Marketplace; code splitting via Vite; throttle drag events in Canvas; compress graph JSON for WS payloads.
- **Accessibility**: ARIA labels e.g., `aria-label="Input port for authentication neuron"` on ports; keyboard support for drag-drop via roving tabindex; screen reader announcements for pathway changes (use live regions); high contrast mode via Tailwind; semantic HTML with role="application" for canvas.
- **Mobile Support**: Touch-friendly drag with hammer.js integration; responsive palette collapses to bottom sheet on small screens; gesture-based connect (long-press ports); tested breakpoints: sm (640px), md (768px), lg (1024px).

## Risks/Gaps

- **WebSocket Reconnect**: Implement auto-reconnect in websocketService with state persistence; risk of desync in collaborative edits—mitigate with conflict resolution (last-write-wins or merge).
- **Offline Editing**: Use IndexedDB via idb-keyval for local graph storage; sync on reconnect via pathwayService, with user notifications for conflicts.
- **Large Graph Rendering**: React-Flow perf drops with 1000+ nodes—plan virtualization or clustering; test with synthetic large graphs in Vitest.
- **Mobile Testing**: Blueprint for device emulation in Chrome DevTools; ensure touch gestures don't conflict with desktop mouse events.
- **Security Gaps**: Validate all JSON configs client-side before send; audit for XSS in AI outputs.
- **AI Latency**: Fallback UI for long generations; credit-based throttling to prevent abuse.

This plan ensures a zero-error build with full functionality, serving as the source of truth for implementation phases.