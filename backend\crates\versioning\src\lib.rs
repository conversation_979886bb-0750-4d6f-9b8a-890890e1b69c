//! Versioning crate for Synapse-IO: SHA256 versioning, audit trails, change tracking

pub mod versioning;
pub mod audit;
pub mod diff;
pub mod storage;

pub use versioning::{VersionManager, Version};
pub use audit::{AuditTrail, AuditEntry};
pub use diff::{compute_diff, DiffResult};
pub use storage::{VersionStorage, save_version};

use synapse_core::SynapseError;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_versioning_imports() {
        // Basic import test for versioning module
        let _version_manager = VersionManager::new();
        let _version = Version::new("1.0.0");
        let _audit = AuditTrail::new();
        let _diff = DiffResult::default();
    }
}