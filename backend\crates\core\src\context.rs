use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use serde_json::Value;

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize, Default)]
pub struct Input {
    pub data: HashMap<String, Value>,
    pub headers: HashMap<String, String>,
    pub query: HashMap<String, String>,
    pub tenant_id: Option<uuid::Uuid>,
}

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize, Default)]
pub struct Output {
    pub data: Value,
    pub status: u16,
    pub headers: HashMap<String, String>,
    pub metadata: HashMap<String, Value>,
}

#[derive(Clone)]
pub struct ExecutionContext {
    pub input: Input,
    pub output: Output,
    pub config: HashMap<String, Value>,
    pub secrets: HashMap<String, String>, // Vault abstraction
    pub tenant_id: uuid::Uuid,
    pub node_id: uuid::Uuid,
    pub execution_id: uuid::Uuid,
}

impl ExecutionContext {
    pub fn new(input: Input) -> Self {
        Self {
            input,
            output: Output::default(),
            config: HashMap::new(),
            secrets: HashMap::new(),
            tenant_id: uuid::Uuid::nil(),
            node_id: uuid::Uuid::nil(),
            execution_id: uuid::Uuid::new_v4(),
        }
    }

    pub fn update_output(&mut self, output: Output) {
        self.output = output;
    }

    pub fn get_secret(&self, key: &str) -> Option<&String> {
        self.secrets.get(key)
    }

    pub fn set_config(&mut self, key: String, value: Value) {
        self.config.insert(key, value);
    }
}