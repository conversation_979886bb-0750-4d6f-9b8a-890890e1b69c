use async_trait::async_trait;
use sqlx::{MySqlPool, Row, Column};
use serde_json::Value;

use crate::traits::Neuron;
use synapse_core::{ExecutionContext, Input, Output, SynapseError};

use uuid::Uuid;

#[derive(Clone)]
pub struct MySqlQuery {
    pool: MySqlPool,
}

impl MySqlQuery {
    pub async fn new(pool_url: &str) -> Result<Self, SynapseError> {
        let pool = sqlx::MySqlPool::connect_lazy(pool_url)
            .map_err(|e: sqlx::Error| SynapseError::Database(format!("Failed to create MySQL pool: {}", e)))?;
        Ok(Self { pool })
    }
}

#[async_trait]
impl Neuron for MySqlQuery {
    fn name(&self) -> &'static str {
        "mysql_query"
    }

    fn description(&self) -> &'static str {
        "Execute MySQL queries with tenant isolation and parameter binding"
    }

    fn version(&self) -> &'static str {
        "1.0.0"
    }

    fn input_schema(&self) -> Option<&'static str> {
        Some(r#"{"type":"object","properties":{"query":{"type":"string","description":"MySQL query with ? placeholders"},"params":{"type":"array","items":{"type":["string","number","boolean","null"]},"description":"Query parameters"},"tenant_id":{"type":"string","description":"Optional tenant UUID for RLS"}},"required":["query"]}"#)
    }

    fn output_schema(&self) -> Option<&'static str> {
        Some(r#"{"type":"object","properties":{"rows":{"type":"array","items":{"type":"object"}},"affected_rows":{"type":"integer"},"last_insert_id":{"type":["integer","null"]}},"required":["rows"]}"#)
    }

    async fn execute(&self, ctx: &mut ExecutionContext) -> Result<Output, SynapseError> {
        let query = ctx.input.data.get("query")
            .and_then(|v| v.as_str())
            .ok_or(SynapseError::InvalidInput("Query parameter required".into()))?
            .to_string();

        let params = ctx.input.data.get("params")
            .and_then(|v| v.as_array())
            .cloned()
            .unwrap_or_default();

        let tenant_id_option = ctx.input.data.get("tenant_id")
            .and_then(|v| v.as_str().map(|s| s.to_string()))
            .or_else(|| if ctx.tenant_id.is_nil() { None } else { Some(ctx.tenant_id.to_string()) });
        let tenant_id_str = tenant_id_option;

        let mut query_builder = sqlx::query(&query);

        // Bind tenant_id for RLS if present (first parameter)
        if let Some(tenant_id) = tenant_id_str {
            query_builder = query_builder.bind(tenant_id);
        }

        // Bind additional parameters
        for param in params {
            match param {
                Value::String(s) => query_builder = query_builder.bind(s),
                Value::Number(n) => {
                    if let Some(i) = n.as_i64() {
                        query_builder = query_builder.bind(i);
                    } else if let Some(f) = n.as_f64() {
                        query_builder = query_builder.bind(f);
                    }
                },
                Value::Bool(b) => query_builder = query_builder.bind(b),
                Value::Null => query_builder = query_builder.bind(None::<String>),
                _ => return Err(SynapseError::InvalidInput("Unsupported parameter type".into())),
            }
        }

        let rows = query_builder
            .fetch_all(&self.pool)
            .await
            .map_err(|e| SynapseError::Database(format!("MySQL query failed: {}", e)))?;

        let results: Vec<Value> = rows.iter().map(|row| {
            let mut map = serde_json::Map::new();
            for column in row.columns() {
                if let Ok(value) = row.try_get::<Value, _>(column.name()) {
                    map.insert(column.name().to_string(), value);
                }
            }
            Value::Object(map)
        }).collect();

        let mut output_data = serde_json::Map::new();
        output_data.insert("rows".to_string(), Value::Array(results));
        output_data.insert("affected_rows".to_string(), Value::Number((rows.len() as u64).into()));

        let mut output = Output::default();
        output.data = Value::Object(output_data);
        output.status = 200;

        Ok(output)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::Value;

    #[tokio::test]
    async fn test_mysql_query_validation() {
        let pool_url = std::env::var("TEST_MYSQL_URL").unwrap_or("mysql://root:password@localhost/test".to_string());
        let neuron = match MySqlQuery::new(&pool_url).await {
            Ok(n) => n,
            Err(_) => return,
        };

        let input_data = [("query".to_string(), Value::String("SELECT 1 as test_value".to_string()))].iter().cloned().collect();
        let input = Input { data: input_data, ..Default::default() };
        let mut ctx = ExecutionContext::new(input);

        let result = neuron.execute(&mut ctx).await;
        match result {
            Ok(output) => {
                assert_eq!(output.status, 200);
                let output_obj = output.data.as_object().unwrap();
                let rows = output_obj["rows"].as_array().unwrap();
                assert_eq!(rows.len(), 1);
            }
            Err(e) => {
                // Connection may fail in test environment, but verify no panic
                assert!(matches!(e, SynapseError::Database(_)));
            }
        }
    }

    #[tokio::test]
    async fn test_mysql_query_no_input() {
        let pool_url = std::env::var("TEST_MYSQL_URL").unwrap_or("mysql://root:password@localhost/test".to_string());
        if let Ok(neuron) = MySqlQuery::new(&pool_url).await {
            let input = Input::default();
            let mut ctx = ExecutionContext::new(input);

            let result = neuron.execute(&mut ctx).await;
            assert!(result.is_err());
            if let Err(SynapseError::InvalidInput(msg)) = result {
                assert!(msg.contains("Query parameter required"));
            }
        }
    }

    #[tokio::test]
    async fn test_mysql_query_invalid_query() {
        let pool_url = std::env::var("TEST_MYSQL_URL").unwrap_or("mysql://root:password@localhost/test".to_string());
        if let Ok(neuron) = MySqlQuery::new(&pool_url).await {
            let input_data = [("invalid".to_string(), Value::String("bad".to_string()))].iter().cloned().collect();
            let input = Input { data: input_data, ..Default::default() };
            let mut ctx = ExecutionContext::new(input);

            let result = neuron.execute(&mut ctx).await;
            assert!(result.is_err());
            if let Err(SynapseError::InvalidInput(msg)) = result {
                assert!(msg.contains("Query parameter required"));
            }
        }
    }

    #[tokio::test]
    async fn test_mysql_query_with_tenant() {
        let pool_url = std::env::var("TEST_MYSQL_URL").unwrap_or("mysql://root:password@localhost/test".to_string());
        if let Ok(neuron) = MySqlQuery::new(&pool_url).await {
            let tenant_uuid = Uuid::parse_str("123e4567-e89b-12d3-a456-************").unwrap();
            let input_data = vec![("query".to_string(), Value::String("SELECT ? as tenant_check".to_string())), ("params".to_string(), Value::Array(vec![Value::String(tenant_uuid.to_string())]))].into_iter().collect();
            let input = Input::default();
            let mut ctx = ExecutionContext::new(input);
            ctx.input.data = input_data;
            ctx.tenant_id = tenant_uuid;

            let result = neuron.execute(&mut ctx).await;
            match result {
                Ok(output) => {
                    assert_eq!(output.status, 200);
                }
                Err(e) => {
                    // Expected in test environment without real DB
                    assert!(matches!(e, SynapseError::Database(_)));
                }
            }
        }
    }

    #[tokio::test]
    async fn test_mysql_connection_error() {
        let invalid_url = "mysql://invalid:invalid@localhost:9999/db";
        let neuron = MySqlQuery::new(invalid_url).await.unwrap();
        let input = Input::default();
        let mut ctx = ExecutionContext::new(input);
        let result = neuron.execute(&mut ctx).await;
        assert!(result.is_err());
        if let Err(SynapseError::Database(e)) = result {
            assert!(e.contains("MySQL query failed"));
        }
    }
}