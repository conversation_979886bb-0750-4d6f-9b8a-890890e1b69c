{"name": "synapse-studio", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-flow-renderer": "^11.10.4", "zustand": "^4.5.2", "axios": "^1.6.0", "socket.io-client": "^4.7.4", "zod": "^3.22.4"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "cypress": "^13.6.3", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "idb": "^7.1.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.6", "typescript": "~5.2.2", "vite": "^5.0.8", "vitest": "^1.1.3"}}