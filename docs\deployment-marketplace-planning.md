
# Synapse-IO Deployment/Export & Marketplace Planning

## Introduction

The deployment and export strategy for Synapse-IO emphasizes flexibility, portability, and no vendor lock-in, supporting multiple hosting models: SaaS via Kubernetes (K8s) with autoscaling for cloud scalability, self-hosted standalone executables (`.exe` on Windows, binaries on Linux/macOS) built with cross-compilation via `cross-rs`, and Docker containers for easy deployment in varied environments. Export functionality allows users to bundle pathways (graphs with embedded neurons and configurations) into lightweight, standalone artifacts that run independently without requiring the full Synapse-IO server.

The marketplace, named "The Cortex," serves as a discovery and monetization platform for custom neurons (WASM modules), enabling developers to upload, users to search/purchase, and Synapse-IO to take a 70/30 revenue split (70% to developers). Monetization includes tiers: free (limited uploads/purchases), pro (unlimited basic neurons), and enterprise (dedicated hosting, SLAs, advanced credits for AI computations).

**Tech Stack Highlights:**
- **CI/CD:** GitHub Actions with matrix strategies for cross-platform builds (`cross-rs` targeting `x86_64-pc-windows-msvc`, `x86_64-unknown-linux-musl`, `x86_64-apple-darwin`); multi-stage Docker builds (Rust 1.75 builder to distroless runtime for minimal footprint).
- **Deployment:** K8s with Helm charts for marketplace components, zero-downtime blue-green deployments via ArgoCD; Docker Compose for self-hosting/local dev.
- **Export:** Bundling via `rust-embed` for static assets (graphs, neurons, config) into `.exe`/binaries with minimal deps (static linking to musl/libc, no external runtime).
- **Marketplace:** Stripe integration via `stripe-rs` 0.4 for payments (checkout sessions, webhooks with idempotency); Tantivy 0.21 for search indexing; semantic versioning in a dedicated crate for conflict resolution and migrations.
- **Design Principles:** Automated CI/CD pipelines ensure signed artifacts (using sigstore/cosign), semantic versioning to prevent lock-in, and compliance-focused operations (SOC2 audit trails for marketplace transactions).

This plan ensures production-ready deploys, with testing covering smoke/load scenarios and risks mitigated through optimization and security best practices.

## Deployment Architecture

### SaaS (Kubernetes with Autoscaling)
- **Cluster Setup:** Deploy to a managed K8s cluster (e.g., EKS/GKE/AKS) with namespaces: `synapse-api` for REST/GraphQL endpoints, `synapse-core` for computation pods, `synapse-ai` for neuron execution (if separated). Use Horizontal Pod Autoscaler (HPA) targeting 70% CPU and 80% memory utilization for scaling (min 2 pods, max 20 per deployment).
- **Secrets Management:** Inject sensitive data (API keys, DB creds, Stripe secrets) via HashiCorp Vault or K8s Secrets, mounted as volumes. Avoid hardcoding; use Sealed Secrets for GitOps.
- **Monitoring:** Integrate Prometheus for metrics (pod health, request latency) and Grafana for dashboards (autoscaling events, error rates). Alert on >5% error rate or scaling thresholds.
- **Deployment Strategy:** Zero-downtime blue-green updates via ArgoCD: promote staging to prod by switching traffic (Ingress controller with weights). Rollouts use `maxUnavailable: 0` and `maxSurge: 25%`.

### Self-Hosted (Standalone Binaries)
- **Build Process:** Static binaries via musl target for Linux/macOS (no glibc deps), MSVC for Windows `.exe`. Config via `config.toml` (e.g., DB URI, port 8080). Run as `./synapse-io` or `synapse-io.exe` with embedded SQLite for single-node (or connect to external Postgres).
- **Portability:** No installation required; single file <50MB, runs on air-gapped systems. Obfuscate with `cargo-obfuscate` in `build.rs` for closed-source protection.

### Docker (Containers for Portability)
- **Build:** Multi-stage Dockerfile: Stage 1 uses `rust:1.75` for cargo build (release mode, musl target); Stage 2 copies binary to distroless (gcr.io/distroless/static-debian12) for runtime (non-root user, no shell). Example: `COPY --from=builder /target/x86_64-unknown-linux-musl/release/synapse-core /synapse-core`.
- **Deployment:** Push images to private registry (ECR/Docker Hub). For self-host: `docker-compose.yaml` with services (api, core, db); volumes for persistence. Local dev: `docker build -t synapse-io . && docker run -p 8080:8080 synapse-io`.

### CI/CD Pipeline
- **Tooling:** GitHub Actions workflow (`.github/workflows/deploy.yml`) triggered on push/PR to `main`/`develop`. Use matrix for platforms: `targets: [x86_64-pc-windows-msvc, x86_64-unknown-linux-musl, x86_64-apple-darwin]`.
- **Steps:**
  1. Checkout code, cache Cargo.
  2. Build cross-platform binaries (`cross build --target $TARGET --release`).
  3. Multi-stage Docker build/push to registry.
  4. Run tests (unit/integration/smoke).
  5. Sign artifacts with cosign (`cosign sign --key env://COSIGN_PRIVATE_KEY`).
  6. Publish `.exe`/bin to S3 (with public ACL for downloads, signed URLs for temp access).
  7. Deploy to staging K8s via ArgoCD; on approval, promote to prod.

**Mermaid Diagram: Deployment Pipeline**

```mermaid
graph TD
    A[Git Push/PR] --> B[Checkout & Cache Cargo]
    B --> C[Build Cross-rs Binaries<br/>Matrix: Windows/Linux/macOS]
    C --> D[Multi-stage Docker Build<br/>Rust Builder -> Distroless Runtime]
    D --> E[Run Tests<br/>Unit/Integration/Smoke]
    E --> F[Sign Artifacts<br/>Cosign for .exe/Docker]
    F --> G[Publish .exe to S3<br/>Signed URLs]
    G --> H[Push Docker to Registry<br/>ECR/Docker Hub]
    H --> I[Deploy Staging K8s<br/>ArgoCD Blue-Green]
    I --> J[Manual Approval]
    J --> K[Promote to Prod K8s<br/>Zero-Downtime]
    K --> L[Monitor with Prometheus/Grafana]
```

## Export Functionality

### Pathway Export
- **User Flow:** In `CanvasPage.tsx`, export button triggers API call to `/export/pathway/:id` (POST with GraphJSON payload). Backend bundles graph, selected neurons (WASM blobs), and config into a standalone artifact.
- **Bundling Process:** Use `rust-embed` crate to embed assets statically: `fn bundle_pathway(graph: GraphJSON, neurons: Vec<WasmBlob>, config: Config) -> Vec<u8>`. Compile core logic into binary with embedded data (no runtime fetch). Output: `.exe` (Windows), binary (Linux/macOS), or Docker image (optional).
- **Standalone Runtime:** Executable includes minimal deps (static musl linking, only libc). Runs isolated: loads embedded graph/neurons, executes pathways offline. No Synapse-IO server needed; config overrides via CLI flags (e.g., `./exported-pathway.exe --input data.json`).

### Optimization and Testing
- **Size Control:** Target <50MB via UPX compression in CI (`upx --best target/release/synapse-export.exe`). Strip symbols, minimize embeds.
- **Testing:** Smoke tests verify standalone run (e.g., `cargo test --test export_smoke` spins up binary, asserts output matches expected). Integration: Mock graph, bundle, execute in Docker for cross-platform validation. Load tests simulate 100 concurrent exports.

**Mermaid Diagram: Export Pipeline**

```mermaid
graph LR
    A[Frontend Export Button<br/>CanvasPage.tsx] --> B[API POST /export/pathway/:id<br/>GraphJSON Payload]
    B --> C[Backend Bundle<br/>rust-embed: Graph + Neurons + Config]
    C --> D[Compile Standalone Binary<br/>Static musl Linking]
    D --> E[Sign Artifact<br/>Cosign]
    E --> F[Compress with UPX<br/>Size <50MB]
    F --> G[Return Blob Download<br/>.exe or Binary]
    G --> H[Standalone Run<br/>No Server Deps]
```

## Marketplace Details

### Upload (Developer Side)
- **Process:** Developers POST to `/neurons/upload` (multipart: WASM code, metadata JSON {name, desc, category, price, version}). Backend validates