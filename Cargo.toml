[workspace]
members = [
    "backend/crates/core",
    "backend/crates/neurons"
]
resolver = "2"

[workspace.dependencies]
tokio = { version = "1.0", features = ["full"] }
axum = "0.7"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono"] }
reqwest = { version = "0.11", features = ["json"] }
serde = { version = "1.0", features = ["derive"] }
jsonwebtoken = "9.3"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }
tungstenite = "0.21"
redis = { version = "0.25", features = ["tokio-comp"] }
actix = "0.13"
loom = "0.7"
pgp = "0.10"
wiremock = "0.6"
proptest = "1.4"
uuid = { version = "1.7", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
tracing = "0.1"
tracing-subscriber = "0.3"
serde_json = { version = "1.0", features = ["arbitrary_precision"] }
validator = { version = "0.16", features = ["derive"] }
async-trait = "0.1"
thiserror = "1.0"