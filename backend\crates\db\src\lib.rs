//! Database crate for Synapse-IO: SQLx PostgreSQL with RLS, Redis pub/sub

pub mod models;
pub mod migrations;
pub mod pool;
pub mod queries;

pub use models::{User, Message, Room, Pathway};
pub use pool::{DatabasePool, create_pool};
pub use queries::{execute_query, execute_migration};

use synapse_core::SynapseError;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_db_imports() {
        // Basic import test for database module
        let _pool = create_pool();
        let _user = User::default();
        let _message = Message::default();
        let _room = Room::default();
        let _pathway = Pathway::default();
    }
}