#[cfg(test)]
mod tests {
    use super::*;
    use proptest::prelude::*;
    use loom::model;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_simple_graph() {
        let start_id = uuid::Uuid::new_v4();
        let mut graph = Graph::new(start_id);
        
        let node_id = uuid::Uuid::new_v4();
        graph.add_node(node_id, "test_node".to_string());
        graph.add_edge(start_id, node_id);
        
        let input = Input::default();
        let result = graph.execute(input).await;
        assert!(result.is_ok());
    }

    proptest! {
        #[test]
        fn proptest_no_cycles(nodes in 1..10_usize, edges in 0..20_usize) {
            let start_id = uuid::Uuid::new_v4();
            let mut graph = Graph::new(start_id);
            
            let node_ids: Vec<uuid::Uuid> = (0..nodes).map(|_| uuid::Uuid::new_v4()).collect();
            for id in &node_ids {
                graph.add_node(*id, "test".to_string());
            }
            
            // Generate random edges without cycles (simplified)
            for _ in 0..edges {
                if let (Some(from), Some(to)) = (node_ids.choose(&mut rand::thread_rng()), node_ids.choose(&mut rand::thread_rng())) {
                    if from != to {
                        graph.add_edge(*from, *to);
                    }
                }
            }
            
            let input = Input::default();
            let result = graph.execute(input).await;
            prop_assert!(result.is_ok(), "Graph execution should succeed without cycles");
        }
    }

    #[cfg_attr(not(miri), loom::model)]
    fn loom_test_concurrent_add() {
        let start_id = uuid::Uuid::new_v4();
        let graph = Arc::new(std::sync::Mutex::new(Graph::new(start_id)));
        
        loom::spawn(move || {
            let mut graph = graph.lock().unwrap();
            let node_id = uuid::Uuid::new_v4();
            graph.add_node(node_id, "loom_node".to_string());
        });
        
        loom::spawn(move || {
            let mut graph = graph.lock().unwrap();
            let node_id = uuid::Uuid::new_v4();
            graph.add_node(node_id, "loom_node2".to_string());
        });
        
        loom::model::spawn_looms();
    }

    #[test]
    fn test_topo_sort_cycle_detection() {
        let start_id = uuid::Uuid::new_v4();
        let mut graph = Graph::new(start_id);
        
        let node1 = uuid::Uuid::new_v4();
        let node2 = uuid::Uuid::new_v4();
        
        graph.add_node(node1, "node1".to_string());
        graph.add_node(node2, "node2".to_string());
        graph.add_edge(node1, node2);
        graph.add_edge(node2, node1); // Create cycle
        
        let result = graph.topo_sort(start_id);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), SynapseError::GraphCycle));
    }

    #[test]
    fn test_graph_add_edge() {
        let start_id = uuid::Uuid::new_v4();
        let mut graph = Graph::new(start_id);
        
        let node1 = uuid::Uuid::new_v4();
        let node2 = uuid::Uuid::new_v4();
        
        graph.add_node(node1, "node1".to_string());
        graph.add_node(node2, "node2".to_string());
        graph.add_edge(node1, node2);
        
        assert_eq!(graph.edges.len(), 1);
        assert_eq!(graph.edges[0].0, node1);
        assert_eq!(graph.edges[0].1, node2);
    }

    #[tokio::test]
    async fn test_execution_context_integration() {
        let start_id = uuid::Uuid::new_v4();
        let mut graph = Graph::new(start_id);
        
        let node_id = uuid::Uuid::new_v4();
        graph.add_node(node_id, "test".to_string());
        
        let mut input = Input::default();
        input.data.insert("test_key".to_string(), serde_json::Value::String("test_value".to_string()));
        
        let result = graph.execute(input).await.unwrap();
        assert_eq!(result.status, 200);
    }

    #[test]
    fn test_recoverable_errors() {
        let timeout = SynapseError::Timeout;
        let rate_limit = SynapseError::RateLimitExceeded;
        let validation = SynapseError::Validation("test".to_string());
        
        assert!(timeout.is_recoverable());
        assert!(rate_limit.is_recoverable());
        assert!(!validation.is_recoverable());
    }
}