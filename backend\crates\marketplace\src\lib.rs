//! Marketplace crate for Synapse-IO: NDK WASM publishing, neuron marketplace

pub mod ndk;
pub mod wasm;
pub mod publishing;
pub mod discovery;

pub use ndk::{NdkClient, publish_neuron};
pub use wasm::{compile_wasm, validate_wasm};
pub use publishing::{publish_to_marketplace, MarketplaceConfig};
pub use discovery::{discover_neurons, NeuronMarketEntry};

use synapse_core::SynapseError;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_marketplace_imports() {
        // Basic import test for marketplace module
        let _ndk = NdkClient::new("test_key".to_string());
        let _config = MarketplaceConfig::default();
        let _market_entry = NeuronMarketEntry::default();
    }
}