use reqwest::Client;
use serde::{Deserialize, Serialize};
use async_trait::async_trait;
use serde_json::Value;

use crate::traits::Neuron;
use synapse_core::{ExecutionContext, Output, SynapseError};

#[derive(Serialize, Deserialize, Debug, Clone)]
struct DiscordToken {
    access_token: String,
    token_type: String,
    expires_in: u32,
    refresh_token: Option<String>,
    scope: String,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
struct DiscordUser {
    id: String,
    username: String,
    email: Option<String>,
    verified: bool,
    avatar: Option<String>,
}

#[derive(Clone)]
pub struct Oauth2Discord;

impl Default for Oauth2Discord {
    fn default() -> Self {
        Self::new()
    }
}

impl Oauth2Discord {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Neuron for Oauth2Discord {
    fn name(&self) -> &'static str {
        "oauth2_discord"
    }

    fn description(&self) -> &'static str {
        "OAuth2 authentication with Discord API"
    }

    fn version(&self) -> &'static str {
        "1.0.0"
    }

    fn input_schema(&self) -> Option<&'static str> {
        Some(r#"{"type":"object","properties":{"code":{"type":"string","description":"OAuth2 authorization code from Discord"}},"required":["code"]}"#)
    }

    fn output_schema(&self) -> Option<&'static str> {
        Some(r#"{"type":"object","properties":{"id":{"type":"string"},"username":{"type":"string"},"email":{"type":["string","null"]},"verified":{"type":"boolean"},"avatar":{"type":["string","null"]},"access_token":{"type":"string"},"expires_in":{"type":"integer"},"scope":{"type":"string"}},"required":["id","username","verified","access_token"]}"#)
    }

    async fn execute(&self, ctx: &mut ExecutionContext) -> Result<Output, SynapseError> {
        let code = ctx.input.data.get("code")
            .and_then(|v| v.as_str())
            .ok_or(SynapseError::InvalidInput("Missing code".into()))?;

        let client_id = std::env::var("DISCORD_CLIENT_ID").map_err(|_| SynapseError::InvalidInput("Missing DISCORD_CLIENT_ID".into()))?;
        let client_secret = std::env::var("DISCORD_CLIENT_SECRET").map_err(|_| SynapseError::InvalidInput("Missing DISCORD_CLIENT_SECRET".into()))?;
        let redirect_uri = std::env::var("DISCORD_REDIRECT_URI").map_err(|_| SynapseError::InvalidInput("Missing DISCORD_REDIRECT_URI".into()))?;

        let client = Client::new();

        let params = [
            ("client_id", client_id.as_str()),
            ("client_secret", client_secret.as_str()),
            ("grant_type", "authorization_code"),
            ("code", code),
            ("redirect_uri", redirect_uri.as_str()),
        ];

        let resp = client
            .post("https://discord.com/api/oauth2/token")
            .form(&params)
            .send()
            .await
            .map_err(|e| SynapseError::Http(format!("Token request failed: {}", e)))?
            .json::<DiscordToken>()
            .await
            .map_err(|e| SynapseError::Http(format!("Token parse failed: {}", e)))?;

        let user_resp = client
            .get("https://discord.com/api/users/@me")
            .bearer_auth(&resp.access_token)
            .send()
            .await
            .map_err(|e| SynapseError::Http(format!("User request failed: {}", e)))?
            .json::<DiscordUser>()
            .await
            .map_err(|e| SynapseError::Http(format!("User parse failed: {}", e)))?;

        let mut user_value = serde_json::to_value(&user_resp)
            .map_err(|e| SynapseError::InvalidInput(format!("Failed to serialize user data: {}", e)))?;

        if let Value::Object(user_obj) = &mut user_value {
            user_obj.insert("access_token".to_string(), Value::String(resp.access_token.clone()));
            user_obj.insert("expires_in".to_string(), Value::Number((resp.expires_in as u64).into()));
            user_obj.insert("scope".to_string(), Value::String(resp.scope.clone()));
        }

        let mut output = Output::default();
        output.data = user_value;
        output.status = 200;

        Ok(output)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use synapse_core::Input;
    use std::collections::HashMap;
    use serde_json::Value;

    #[tokio::test]
    async fn test_oauth2_discord_missing_code() {
        std::env::set_var("DISCORD_CLIENT_ID", std::env::var("TEST_DISCORD_CLIENT_ID").unwrap_or("mock_client_id".to_string()));
        std::env::set_var("DISCORD_CLIENT_SECRET", std::env::var("TEST_DISCORD_CLIENT_SECRET").unwrap_or("mock_client_secret".to_string()));
        std::env::set_var("DISCORD_REDIRECT_URI", std::env::var("TEST_DISCORD_REDIRECT_URI").unwrap_or("http://localhost/callback".to_string()));

        let neuron = Oauth2Discord::new();

        let input_data = HashMap::new(); // Missing code
        let input = Input { data: input_data, ..Default::default() };
        let mut ctx = ExecutionContext::new(input);

        let result = neuron.execute(&mut ctx).await;

        assert!(result.is_err());
        if let Err(SynapseError::InvalidInput(msg)) = result {
            assert!(msg.contains("Missing code"));
        }
    }

    #[tokio::test]
    async fn test_oauth2_discord_metadata() {
        let neuron = Oauth2Discord::new();

        assert_eq!(neuron.name(), "oauth2_discord");
        assert_eq!(neuron.description(), "OAuth2 authentication with Discord API");
        assert_eq!(neuron.version(), "1.0.0");

        if let Some(input_schema) = neuron.input_schema() {
            assert!(input_schema.contains("code"));
            assert!(input_schema.contains("required"));
        }

        if let Some(output_schema) = neuron.output_schema() {
            assert!(output_schema.contains("id"));
            assert!(output_schema.contains("username"));
            assert!(output_schema.contains("access_token"));
        }
    }

    #[tokio::test]
    async fn test_oauth2_discord_structure() {
        std::env::set_var("DISCORD_CLIENT_ID", std::env::var("TEST_DISCORD_CLIENT_ID").unwrap_or("mock_client_id".to_string()));
        std::env::set_var("DISCORD_CLIENT_SECRET", std::env::var("TEST_DISCORD_CLIENT_SECRET").unwrap_or("mock_client_secret".to_string()));
        std::env::set_var("DISCORD_REDIRECT_URI", std::env::var("TEST_DISCORD_REDIRECT_URI").unwrap_or("http://localhost/callback".to_string()));

        let neuron = Oauth2Discord::new();

        let input_data = [("code".to_string(), Value::String("valid_code".to_string()))].iter().cloned().collect();
        let input = Input { data: input_data, ..Default::default() };
        let mut ctx = ExecutionContext::new(input);

        let result = neuron.execute(&mut ctx).await;
        
        // Expect HTTP error due to invalid Discord API call, but verify no panic
        if let Err(SynapseError::Http(_)) = result {
            // Expected HTTP error for unit test
        } else {
            let output = result.expect("Execution should return Output or expected error");
            assert_eq!(output.status, 200);
        }
    }
}