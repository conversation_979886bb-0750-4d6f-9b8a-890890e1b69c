//! Scalability crate for Synapse-IO: Actix actor pools, HPA scaling, load balancing

pub mod actors;
pub mod pools;
pub mod scaling;
pub mod metrics;

pub use actors::{WorkerActor, GraphExecutorActor};
pub use pools::{ActorPool, create_worker_pool};
pub use scaling::{HorizontalScaler, ScaleConfig};
pub use metrics::{PerformanceMetrics, collect_metrics};

use synapse_core::SynapseError;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scalability_imports() {
        // Basic import test for scalability module
        let _worker_pool = create_worker_pool(4);
        let _scale_config = ScaleConfig::default();
        let _metrics = PerformanceMetrics::default();
    }
}