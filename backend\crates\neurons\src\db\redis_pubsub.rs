use crate::error::SynapseError;
use crate::Neuron;
use crate::ExecutionContext;
use crate::Output;
use crate::Input;
use async_trait::async_trait;
use redis::{aio::AsyncCommands, Client, RedisError};
use redis::aio::ConnectionManager;
use serde_json::{json, Value};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicI32, AtomicUsize, Ordering};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::Barrier;
use tokio::time::{sleep, timeout};
use uuid::Uuid;

const TIMEOUT_SECS: u64 = 30;
const RETRIES: u32 = 3;
const RATE_LIMIT_PUBS_PER_SEC: usize = 10;
const MAX_BACKOFF_MS: u64 = 1000;

#[derive(Clone)]
pub struct RedisPubSub {
    client: ConnectionManager,
}

impl RedisPubSub {
    pub async fn new(redis_url: &str) -> Result<Self, SynapseError> {
        let client = Client::open(redis_url)
            .map_err(|e| SynapseError::Database(format!("Redis client creation failed: {}", e)))?;
        let manager = ConnectionManager::new(client).await
            .map_err(|e| SynapseError::Database(format!("Connection manager failed: {}", e)))?;
        Ok(Self { client: manager })
    }

    pub fn prefix_channel(&self, channel: &str, tenant_id: Option<&Uuid>) -> Result<String, SynapseError> {
        if let Some(tenant) = tenant_id {
            if tenant.is_nil() {
                return Err(SynapseError::InvalidInput("Nil tenant UUID rejected for channel prefixing".to_string()));
            }
            Ok(format!("{}_{}", tenant, channel))
        } else {
            Ok(channel.to_string())
        }
    }

    fn is_transient_error(error: &RedisError) -> bool {
        matches!(error.kind(), redis::ErrorKind::IoError | redis::ErrorKind::TimedOut | redis::ErrorKind::ExtensionError)
    }

        async fn execute_with_retries<F, T>(&self, mut op: F) -> Result<T, SynapseError>
    where
        F: FnMut(&mut ConnectionManager) -> futures::future::BoxFuture<'_, Result<T, RedisError>>,
    {
        let mut backoff = 100u64;
        for attempt in 0..RETRIES {
            match timeout(
                Duration::from_secs(TIMEOUT_SECS),
                op(&mut self.client.clone()),
            )
            .await
            {
                Ok(Ok(result)) => return Ok(result),
                Ok(Err(e)) if attempt + 1 < RETRIES && Self::is_transient_error(&e) => {
                    sleep(Duration::from_millis(backoff.min(MAX_BACKOFF_MS))).await;
                    backoff *= 2;
                }
                Ok(Err(e)) => {
                    return Err(SynapseError::Database(format!("Redis operation failed: {}", e)));
                }
                Err(_) => {
                    if attempt + 1 < RETRIES {
                        sleep(Duration::from_millis(backoff.min(MAX_BACKOFF_MS))).await;
                        backoff *= 2;
                    } else {
                        return Err(SynapseError::Database("Redis operation timed out".to_string()));
                    }
                }
            }
        }
        unreachable!()
    }

    async fn get_subscribers_count(&self, channel: &str) -> Result<usize, SynapseError> {
        let prefixed = self.prefix_channel(channel, None)?;
        let counts: Vec<(String, usize)> = self
            .execute_with_retries(|c| Box::pin(async move {
                let mut pipe = redis::pipe();
                pipe.atomic()
                    .pubsub_numsub(prefixed.as_str())
                    .ignore()
                    .pubsub_channels()
                    .ignore();
                pipe.query_async(c.as_mut()).await
            }))
            .await
            .unwrap_or_default();
        Ok(counts.into_iter().find(|(c, _)| c == prefixed).map(|(_, n)| n).unwrap_or(0))
    }
}

#[async_trait]
impl Neuron for RedisPubSub {
    fn name(&self) -> &'static str {
        "redis_pubsub"
    }

    fn description(&self) -> &'static str {
        "Async Redis Pub/Sub with tenant isolation, rate limiting, and idempotent unsubscribe"
    }

    fn version(&self) -> &'static str {
        "2.0.0"
    }

    fn input_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "action": {"type": "string", "enum": ["publish", "subscribe", "unsubscribe", "psubscribe"]},
                "channel": {"type": "string"},
                "pattern": {"type": "string"},
                "message": {"type": "string"},
                "data": {"type": "object"},
                "tenant_id": {"type": "string", "format": "uuid"}
            },
            "required": ["action", "channel"]
        })
    }

    fn output_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "channel": {"type": "string"},
                "subscribers": {"type": "integer"},
                "active_patterns": {"type": "integer"},
                "pre_subscribers": {"type": "integer"},
                "delta_subscribers": {"type": "integer"},
                "rate_limited": {"type": "boolean"},
                "tenant_isolated": {"type": "boolean"},
                "race_detected": {"type": "boolean"},
                "error": {"type": "string"}
            }
        })
    }

    async fn execute(&self, input: Value, mut ctx: ExecutionContext) -> Result<Output, SynapseError> {
        let input_map = input.as_object().ok_or(SynapseError::InvalidInput("Input must be JSON object".to_string()))?;
        let action = input_map.get("action").and_then(|v| v.as_str()).ok_or(SynapseError::InvalidInput("Missing action".to_string()))?;
        let channel = input_map.get("channel").and_then(|v| v.as_str()).ok_or(SynapseError::InvalidInput("Missing channel".to_string()))?;
        let tenant_str = input_map.get("tenant_id").and_then(|v| v.as_str());
        let tenant_id = tenant_str.and_then(|s| Uuid::parse_str(s).ok());
        let message = input_map.get("message").and_then(|v| v.as_str()).unwrap_or("");
        let data = input_map.get("data").cloned().unwrap_or(json!({}));

        let mut output = Output::default();
        output.data = json!({
            "success": false,
            "channel": channel,
            "subscribers": 0,
            "active_patterns": 0,
            "pre_subscribers": 0,
            "delta_subscribers": 0,
            "rate_limited": false,
            "tenant_isolated": tenant_id.is_some(),
            "race_detected": false,
            "error": ""
        });

        let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
        let rate_key = format!("rate:{}", channel);
        let mut rate_limited = false;

        if action == "publish" {
            if let Some(times) = ctx.output.metadata.get(&rate_key).and_then(|v| v.as_array()) {
                let recent: Vec<_> = times.iter().filter_map(|t| t.as_u64()).filter(|&t| now.saturating_sub(t) < 1).collect();
                if recent.len() >= RATE_LIMIT_PUBS_PER_SEC {
                    rate_limited = true;
                    output.data["success"] = json!(false);
                    output.data["error"] = json!("Rate limited: too many publishes");
                    return Ok(output);
                }
                let mut new_times = recent;
                new_times.push(now);
                ctx.output.metadata.insert(rate_key.clone(), json!(new_times));
            } else {
                ctx.output.metadata.insert(rate_key, json!([now]));
            }
        }

        let prefixed_channel = self.prefix_channel(channel, tenant_id.as_ref())?;
        let tenant_isolated = tenant_id.is_some();

        match action {
            "publish" => {
                let msg = if message.is_empty() { data.to_string() } else { message.to_string() };
                self.execute_with_retries(|c| Box::pin(async move {
                    c.publish(prefixed_channel.as_str(), msg.as_str()).await
                }))
                .await?;
                output.data["success"] = json!(true);
                output.data["rate_limited"] = json!(rate_limited);
            }
            "subscribe" => {
                self.execute_with_retries(|c| Box::pin(async move {
                    c.subscribe(prefixed_channel.as_str()).await
                }))
                .await?;
                let subscribers = self.get_subscribers_count(&prefixed_channel).await?;
                output.data["success"] = json!(true);
                output.data["subscribers"] = json!(subscribers);
            }
            "unsubscribe" => {
                let pre_subscribers = self.get_subscribers_count(&prefixed_channel).await?;
                output.data["pre_subscribers"] = json!(pre_subscribers);
                self.execute_with_retries(|c| Box::pin(async move {
                    c.unsubscribe(prefixed_channel.as_str()).await
                }))
                .await?;
                let post_subscribers = self.get_subscribers_count(&prefixed_channel).await?;
                let delta = pre_subscribers as isize - post_subscribers as isize;
                output.data["delta_subscribers"] = json!(delta);
                output.data["success"] = json!(true);
                if post_subscribers > pre_subscribers {
                    eprintln!("Race detected in unsubscribe: {} -> {}", pre_subscribers, post_subscribers);
                    output.data["race_detected"] = json!(true);
                }
            }
            "psubscribe" => {
                let pattern = input_map.get("pattern").and_then(|v| v.as_str()).ok_or(SynapseError::InvalidInput("Missing pattern for psubscribe".to_string()))?;
                if !pattern.starts_with('*') && !pattern.starts_with(&format!("{}_", tenant_id.unwrap_or(Uuid::nil()))) {
                    return Err(SynapseError::InvalidInput("PSUBSCRIBE pattern must start with tenant prefix or *".to_string()));
                }
                self.execute_with_retries(|c| Box::pin(async move {
                    c.psubscribe(pattern).await
                }))
                .await?;
                let patterns = self.execute_with_retries(|c| Box::pin(async move {
                    let mut pipe = redis::pipe();
                    pipe.atomic().pubsub_numpat().ignore();
                    pipe.query_async::<_, usize>(c.as_mut()).await
                })).await?;
                output.data["success"] = json!(true);
                output.data["active_patterns"] = json!(patterns);
            }
            _ => return Err(SynapseError::InvalidInput(format!("Unknown action: {}", action))),
        }

        output.data["tenant_isolated"] = json!(tenant_isolated);
        Ok(output)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use proptest::prelude::*;

    async fn setup_neuron() -> RedisPubSub {
        RedisPubSub::new("redis://localhost:6379").await.expect("Failed to create neuron")
    }

    fn create_context() -> ExecutionContext {
        ExecutionContext {
            input: Input::default(),
            output: Output::default(),
            metadata: HashMap::new(),
            ..Default::default()
        }
    }

    #[tokio::test]
    async fn test_publish_success() {
        let neuron = setup_neuron().await;
        let mut ctx = create_context();
        let input = json!({
            "action": "publish",
            "channel": "test_channel",
            "message": "hello"
        });
        let result = neuron.execute(input, ctx).await;
        assert!(matches!(result, Err(SynapseError::Database(_))));
    }

    #[tokio::test]
    async fn test_subscribe_success() {
        let neuron = setup_neuron().await;
        let mut ctx = create_context();
        let input = json!({
            "action": "subscribe",
            "channel": "test_channel"
        });
        let result = neuron.execute(input, ctx).await;
        assert!(matches!(result, Err(SynapseError::Database(_))));
    }

    #[tokio::test]
    async fn test_unsubscribe_idempotent() {
        let neuron = setup_neuron().await;
        let mut ctx = create_context();
        let input = json!({
            "action": "unsubscribe",
            "channel": "test_channel"
        });
        let result = neuron.execute(input, ctx).await;
        if let Ok(output) = result {
            assert!(output.data.get("pre_subscribers").is_some());
            assert!(output.data.get("delta_subscribers").is_some());
        }
    }

    #[tokio::test]
    async fn test_psubscribe_pattern_valid() {
        let neuron = setup_neuron().await;
        let mut ctx = create_context();
        let tenant = Uuid::new_v4();
        let input = json!({
            "action": "psubscribe",
            "channel": "test",
            "pattern": format!("{}_*", tenant),
            "tenant_id": tenant.to_string()
        });
        let result = neuron.execute(input, ctx).await;
        assert!(matches!(result, Err(SynapseError::Database(_))));
    }

    #[tokio::test]
    async fn test_psubscribe_pattern_invalid() {
        let neuron = setup_neuron().await;
        let input = json!({
            "action": "psubscribe",
            "channel": "test",
            "pattern": "invalid_pattern"
        });
        let result = neuron.execute(input, create_context()).await;
        assert!(matches!(result, Err(SynapseError::InvalidInput(_))));
    }

    #[tokio::test]
    async fn test_nil_tenant_rejected() {
        let neuron = setup_neuron().await;
        let input = json!({
            "action": "publish",
            "channel": "test",
            "tenant_id": Uuid::nil().to_string()
        });
        let result = neuron.execute(input, create_context()).await;
        assert!(matches!(result, Err(SynapseError::InvalidInput(_))));
    }

    #[tokio::test]
    async fn test_rate_limiting() {
        let neuron = setup_neuron().await;
        let mut ctx = create_context();
        for _ in 0..12 {
            let input = json!({
                "action": "publish",
                "channel": "rate_test"
            });
            let _ = neuron.execute(input.clone(), ctx.clone()).await;
        }
        assert!(ctx.output.metadata.contains_key("rate:rate_test"));
    }

    #[tokio::test]
    async fn test_input_schema() {
        let neuron = setup_neuron().await;
        let schema = neuron.input_schema();
        assert!(schema.get("required").unwrap().as_array().unwrap().contains(&json!("action")));
    }

    #[tokio::test]
    async fn test_output_schema() {
        let neuron = setup_neuron().await;
        let schema = neuron.output_schema();
        assert!(schema.get("properties").unwrap().get("success").is_some());
    }

    #[tokio::test]
    async fn test_unknown_action() {
        let neuron = setup_neuron().await;
        let input = json!({
            "action": "invalid",
            "channel": "test"
        });
        let result = neuron.execute(input, create_context()).await;
        assert!(matches!(result, Err(SynapseError::InvalidInput(_))));
    }

        #[tokio::test]
    async fn test_missing_channel() {
        let neuron = setup_neuron().await;
        let input = json!({
            "action": "publish"
        });
        let result = neuron.execute(input, create_context()).await;
        assert!(matches!(result, Err(SynapseError::InvalidInput(_))));
    }
}

#[cfg(test)]
mod proptest_tests {
    use super::*;
    use proptest::prelude::*;

    proptest! {
        #[test]
        fn prop_prefix_channel_valid(tenant_str in r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}", channel in "[a-zA-Z0-9_-]+") {
            let tenant = Uuid::parse_str(&tenant_str).unwrap();
            let prefix = format!("{}_", tenant);
            let expected = format!("{}_{}", tenant, channel);
            let neuron = RedisPubSub {
                client: ConnectionManager::new(Client::open("redis://localhost:6379").unwrap())
            };
            let prefixed = neuron.prefix_channel(&channel, Some(&tenant)).unwrap();
            prop_assert_eq!(prefixed, expected);
            prop_assert!(prefixed.starts_with(&prefix));
            prop_assert!(prefixed.ends_with(&channel));
        }
    }

    proptest! {
        #[test]
        fn prop_rate_limit_logic(pubs in 5..15usize) {
            let mut metadata = HashMap::new();
            let channel = "prop_rate";
            let rate_key = format!("rate:{}", channel);
            let now = 1000u64;
            
            let mut recent_count = 0;
            for i in 0..pubs {
                let times = metadata.get(&rate_key).and_then(|v| v.as_array());
                let recent: Vec<_> = times.map(|arr| {
                    arr.iter().filter_map(|t| t.as_u64()).filter(|&t| now - t < 1).collect()
                }).unwrap_or_default();
                
                recent_count = recent.len();
                if recent_count < RATE_LIMIT_PUBS_PER_SEC {
                    let mut new_times = recent;
                    new_times.push(now - i as u64); // timestamps within 1 second
                    metadata.insert(rate_key.clone(), json!(new_times));
                }
            }
            
            prop_assert!(recent_count <= RATE_LIMIT_PUBS_PER_SEC);
        }
    }

    proptest! {
        #[test]
        fn prop_psubscribe_pattern_valid(pattern in "[a-z*]+", tenant_str in r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}") {
            let tenant = Uuid::parse_str(&tenant_str).unwrap();
            let tenant_prefix = format!("{}_", tenant);
            
            let test_pattern = if pattern.starts_with('*') { 
                pattern.to_string() 
            } else { 
                format!("{}_{}", tenant_prefix.trim_end_matches('_'), pattern) 
            };
            
            let is_valid = test_pattern.starts_with('*') || test_pattern.starts_with(&tenant_prefix);
            prop_assert!(is_valid == (pattern.starts_with('*') || test_pattern.starts_with(&tenant_prefix)));
        }
    }

    proptest! {
        #[test]
        fn prop_channel_name_safety(channel in "[a-zA-Z0-9_:-]+") {
            let neuron = RedisPubSub {
                client: ConnectionManager::new(Client::open("redis://localhost:6379").unwrap())
            };
            let prefixed = neuron.prefix_channel(&channel, None).unwrap();
            prop_assert!(!prefixed.is_empty());
            prop_assert!(!prefixed.contains(&[' ', '\t', '\n', '\r'][..]));
            prop_assert!(prefixed.chars().all(|c| matches!(c, 'a'..='z' | 'A'..='Z' | '0'..='9' | '_' | ':' | '-')));
        }
    }

    proptest! {
        #[test]
        fn prop_uuid_tenant_isolation(valid_uuid in r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}") {
            let parsed = Uuid::parse_str(&valid_uuid).unwrap();
            prop_assert!(!parsed.is_nil());
            let neuron = RedisPubSub {
                client: ConnectionManager::new(Client::open("redis://localhost:6379").unwrap())
            };
            let prefixed = neuron.prefix_channel("test", Some(&parsed)).unwrap();
            let prefix = format!("{}_", parsed);
            prop_assert!(prefixed.starts_with(&prefix));
            prop_assert!(prefixed.ends_with("test"));
        }
    }
}

#[cfg(test)]
mod concurrency_tests {
    use super::*;
    use crate::Input;

    #[tokio::test]
    async fn test_parallel_publish() {
        let neuron = setup_neuron().await;
        let mut ctx = create_context();
        let barrier = Arc::new(Barrier::new(5));
        let mut handles = vec![];

        for _ in 0..5 {
            let neuron_clone = neuron.clone();
            let ctx_clone = ctx.clone();
            let barrier_clone = barrier.clone();
            let input = json!({
                "action": "publish",
                "channel": "parallel_channel"
            });
            handles.push(tokio::spawn(async move {
                barrier_clone.wait().await;
                let _ = neuron_clone.execute(input, ctx_clone).await;
            }));
        }

        for handle in handles {
            let _ = handle.await;
        }
    }

    #[tokio::test]
    async fn test_concurrent_subscribe_unsubscribe() {
        let neuron = setup_neuron().await;
        let mut ctx = create_context();
        let barrier = Arc::new(Barrier::new(10));
        let counter = Arc::new(AtomicI32::new(0));
        let mut handles = vec![];

        for i in 0..10 {
            let neuron_clone = neuron.clone();
            let ctx_clone = ctx.clone();
            let barrier_clone = barrier.clone();
            let counter_clone = counter.clone();
                                    let input = json!({
                "action": if i < 5 { "subscribe" } else { "unsubscribe" },
                "channel": "concurrent_channel"
            });
            counter_clone.fetch_add(1, Ordering::SeqCst);
            let _ = neuron_clone.execute(input, ctx_clone).await;
        }
    }

    for handle in handles {
        let _ = handle.await;
    }
    assert_eq!(counter.load(Ordering::SeqCst), 10);
}

#[tokio::test]
async fn test_basic_concurrency() {
    let neuron = setup_neuron().await;
    let mut ctx = create_context();
    let input = json!({
        "action": "publish",
        "channel": "basic_concurrent"
    });
    let result1 = neuron.execute(input.clone(), ctx.clone()).await;
    let result2 = neuron.execute(input, ctx).await;
    assert!(matches!(result1, Err(SynapseError::Database(_))));
    assert!(matches!(result2, Err(SynapseError::Database(_))));
}

#[cfg(test)]
 = vec![
            json!({"action": "invalid"}), // Missing channel
            json!({"action": "publish", "channel": "test", "tenant_id": Uuid::nil().to_string()}), // Nil tenant
            json!({"action": "psubscribe", "channel": "test", "pattern": "invalid_pattern"}), // Invalid pattern
        ];

        for input in inputs {
            let result = neuron.execute(input, create_context()).await;
            assert!(!matches!(result, Ok(_)));
        }
    }
}

#[cfg(test)]

            });
            counter_clone.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
            let _ = neuron_clone.execute(input, ctx_clone).await;
        }
    }

    for handle in handles {
        let _ = handle.await;
    }
    assert_eq!(counter.load(std::sync::atomic::Ordering::SeqCst), 10);
}

#[tokio::test]
async fn test_rate_limit_concurrency() {
    let neuron = setup_neuron().await;
    let mut ctx = create_context();
    let barrier = Arc::new(Barrier::new(RATE_LIMIT_PUBS_PER_SEC + 1));
    let mut handles = vec![];

    for _ in 0..RATE_LIMIT_PUBS_PER_SEC + 1 {
        let neuron_clone = neuron.clone();
        let ctx_clone = ctx.clone();
        let barrier_clone = barrier.clone();
        let input = json!({
            "action": "publish",
            "channel": "rate_concurrent"
        });
        handles.push(tokio::spawn(async move {
            barrier_clone.wait().await;
            let _ = neuron_clone.execute(input, ctx_clone).await;
        }));
    }

    for handle in handles {
        let _ = handle.await;
    }
}

#[tokio::test]
async fn test_parallel_operations() {
    let neuron = setup_neuron().await;
    let barrier = Arc::new(Barrier::new(8));
    let mut handles = vec![];

    let actions = vec!["publish", "subscribe", "unsubscribe", "psubscribe"];
    for (i, action) in actions.iter().cycle().take(8).enumerate() {
        let neuron_clone = neuron.clone();
        let barrier_clone = barrier.clone();
        let input = match action.as_str() {
            "psubscribe" => json!({
                "action": action,
                "channel": "parallel",
                "pattern": "*",
                "tenant_id": Uuid::new_v4().to_string()
            }),
            _ => json!({
                "action": action,
                "channel": format!("parallel_{}", i % 4)
            }),
        };
        handles.push(tokio::spawn(async move {
            barrier_clone.wait().await;
            let _ = neuron_clone.execute(input, create_context()).await;
        }));
    }

    for handle in handles {
        let _ = handle.await;
    }
}

#[cfg(test)]
mod integration_tests