//! API crate for Synapse-IO: Axum routes, Tower middleware, WebSocket handling

pub mod routes;
pub mod middleware;
pub mod ws;

pub use routes::create_router;
pub use middleware::{auth_middleware, rate_limit_middleware, validation_middleware};
pub use ws::websocket_handler;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_imports() {
        // Basic import test for API module
        let _router = create_router();
        let _auth = auth_middleware();
        let _rate_limit = rate_limit_middleware();
        let _validation = validation_middleware();
        let _ws_handler = websocket_handler();
    }
}