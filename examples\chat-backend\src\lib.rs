//! Chat Backend Example for Synapse-IO: Complete pathway implementation with neurons

pub mod pathways;
pub mod auth;
pub mod chat;
pub mod premium;
pub mod admin;

pub use pathways::{ChatPathway, execute_chat_pathway};
pub use auth::authenticate_user;
pub use chat::{process_message, moderate_message};
pub use premium::handle_premium_feature;
pub use admin::admin_operations;

use synapse_core::{ExecutionContext, SynapseError};

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_chat_backend_imports() {
        // Basic import test for chat backend
        let _context = ExecutionContext::default();
        let _pathway = ChatPathway::new();
    }
}