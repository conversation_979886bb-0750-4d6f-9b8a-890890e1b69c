# Synapse-IO Neuron System Planning

## Introduction

Neurons serve as the fundamental modular building blocks within the Synapse-IO system, enabling the construction of pathways that represent visual API workflows. Each neuron is a self-contained, executable unit that processes inputs to produce outputs, adhering to a trait-based design for uniformity and extensibility. The core principle of modularity is achieved through the `Neuron` trait, which defines a standard `execute` method: `fn execute(&self, input: Input) -> Result<Output, SynapseError>`. Neurons are configurable via JSON parameters, allowing dynamic adjustment of behavior without recompilation, and are serializable for export/import across environments. This design ensures pathways can be visually composed in the Synapse Studio frontend (via NeuronNode.tsx components) and executed efficiently in the backend graph runtime.

Modularity is further enforced by organizing neurons into sub-crates or modules within `/backend/crates/neurons`, promoting separation of concerns, reusability, and independent development/testing. Custom neurons are supported through the Neuron Development Kit (NDK), enabling developers to create specialized logic in Rust, Python, or TypeScript, compiled to WASM for sandboxed execution. The system integrates with the Synapse Core graph runtime for asynchronous traversal using Tokio, supports versioning and marketplace distribution via The Cortex, and incorporates robust security measures to prevent vulnerabilities in production deployments.

This plan provides an exhaustive blueprint for the neuron system, ensuring full coverage for use cases like the chat-backend example (e.g., integrating OAuth2 for Discord, PostgreSQL user queries, WebSocket chat handling, and AI moderation).

## Complete Neuron List

The following is an exhaustive list of over 60 required neurons, categorized by type. Each category covers essential functionality for building complete pathways. Neurons are designed to be production-ready, with concrete implementations relying on established crates (e.g., sqlx for DB, reqwest for HTTP). The list expands beyond initial examples to include logging, encryption, caching, pagination, and more, ensuring comprehensive coverage.

### Input/Entry Points
These neurons handle incoming requests from various protocols, parsing and routing data into the pathway.

| Name | Description | Inputs | Outputs | Config Params | Dependencies |
|------|-------------|--------|---------|---------------|--------------|
| rest_ingest | Ingests HTTP REST requests, extracts headers/body. | RawHttpRequest | ParsedRequest<Headers, Body> | port: u16, timeout: Duration | hyper, serde |
| graphql_query | Parses and executes GraphQL queries against schema. | GraphQLQuery<String> | GraphQLResponse<Data, Errors> | schema_path: PathBuf, max_depth: usize | async-graphql, serde |
| websocket_listener | Establishes and listens on WebSocket connections for real-time input. | WsConnection | Stream<WsMessage> | endpoint: String, buffer_size: usize | tokio-tungstenite, serde |
| grpc_handler | Handles gRPC calls, deserializes protobuf messages. | GrpcRequest<ProtoMsg> | GrpcResponse<ProtoMsg> | service_name: String, proto_def: PathBuf | tonic, prost |
| cli_input | Processes command-line inputs for scripted pathways. | CliArgs<Vec<String>> | ParsedCli<Flags, Args> | help_text: String | clap, serde |
| file_ingest | Reads files from local or remote sources as input. | FilePath | FileContent<Bytes> | format: Enum(FileType), chunk_size: usize | tokio::fs, reqwest |
| event_stream | Subscribes to event streams (e.g., Kafka topics). | StreamConfig | EventStream<Item<Event>> | topic: String, bootstrap_servers: Vec<String> | rdkafka, serde |

### Authentication/Security
These neurons manage access control, validation, and threat detection.

| Name | Description | Inputs | Outputs | Config Params | Dependencies |
|------|-------------|--------|---------|---------------|--------------|
| jwt_validate | Validates JWT tokens, extracts claims. | RequestWithToken | ValidatedUser<Claims> | secret_key: String, issuer: String | jsonwebtoken, serde |
| oauth2_flow | Handles OAuth2 authorization code flow. | AuthRequest | AccessToken<RefreshToken> | client_id: String, client_secret: String, provider: Enum(Provider) | oauth2, reqwest |
| sso_redirect | Manages SSO redirects and session initiation. | RedirectUrl | SessionToken | sso_url: String, callback: String | url, serde |
| input_validator | Validates input schemas using JSON Schema. | RawInput | ValidatedInput<Errors> | schema: JsonValue | valico, serde_json |
| rate_limiter | Enforces rate limits per IP/user. | RequestMetadata | Allowed<Or Denied> | limit: u32, window: Duration, storage: RedisUrl | governor, redis |
| anomaly_detector | Detects anomalous behavior using statistical/ML models. | RequestLog | AnomalyScore<Alert> | threshold: f64, model_path: PathBuf | linfa, ndarray |
| encryption_guard | Encrypts sensitive inputs before processing. | PlainData | EncryptedData<Ciphertext> | key: SymmetricKey, algorithm: Enum(AesGcm) | ring, serde |

### Storage/Database
These neurons interact with persistent and cache stores.

| Name | Description | Inputs | Outputs | Config Params | Dependencies |
|------|-------------|--------|---------|---------------|--------------|
| postgres_query | Executes SQL queries on PostgreSQL. | QueryParams<Sql> | QueryResult<Rows> | connection_string: String, pool_size: usize | sqlx, tokio |
| mysql_insert | Performs inserts into MySQL databases. | InsertData<Values> | InsertResult<AffectedRows> | ds: String, table: String | sqlx, serde |
| mongodb_aggregate | Runs aggregation pipelines on MongoDB. | Pipeline<Vec<Stage>> | AggregateResult<Documents> | uri: String, db: String, collection: String | mongodb, bson |
| redis_cache | Gets/sets key-value pairs in Redis. | CacheKey | CacheValue<Or Miss> | redis_url: String, ttl: Duration | redis, serde |
| sqlite_read | Reads from local SQLite databases. | SqliteQuery | SqliteRows | db_path: PathBuf, pragma: String | rusqlite, serde |
| cassandra_write | Writes data to Cassandra clusters. | CassandraRow | WriteResult<Timestamp> | contact_points: Vec<String>, keyspace: String | cdrs-tokio, serde |
| etcd_store | Stores/retrieves from etcd key-value store. | EtcdKey | EtcdValue<Revision> | endpoints: Vec<String> | etcd-client, serde |

### Processing/Business Logic
These neurons handle data transformations, logic flows, and error management.

| Name | Description | Inputs | Outputs | Config Params | Dependencies |
|------|-------------|--------|---------|---------------|--------------|
| json_transform | Applies JSONata transformations to data. | JsonInput | TransformedJson | expression: String, libs: Vec<String> | jsonata-go, serde_json |
| data_validator | Validates data against business rules. | DataPayload | ValidationResult<Issues> | rules: Vec<Rule>, strict: bool | validator, serde |
| conditional_branch | Branches pathway based on conditions. | EvalContext | BranchDecision<Path> | conditions: Vec<IfThen>, default: Path | serde, eval |
| loop_iterator | Iterates over collections with configurable loops. | Iterable<Item> | IteratedResults<Vec<Output>> | max_iterations: usize, batch_size: usize | itertools, tokio |
| error_handler | Catches and processes errors, optionally retries. | ErrorEvent | HandledError<RetryOr Log> | retry_policy: Enum, log_level: Level | thiserror, tracing |
| pagination_handler | Applies pagination to query results. | PagedQuery | PagedResponse<Items, Meta> | page_size: usize, offset: usize | serde, itertools |
| aggregation_reducer | Aggregates data (sum, avg, etc.). | DataStream | AggregateValue<Stats> | operation: Enum(AggOp), group_by: Vec<String> | statrs, ndarray |
| filtering_selector | Filters data based on predicates. | Filterable<Data> | FilteredResults | predicate: String, case_sensitive: bool | predicate, serde |

### Integrations/External APIs
These neurons connect to third-party services.

| Name | Description | Inputs | Outputs | Config Params | Dependencies |
|------|-------------|--------|---------|---------------|--------------|
| discord_webhook | Sends messages via Discord webhooks. | WebhookPayload | SendResult<Id> | webhook_url: String, retries: usize | reqwest, serde |
| slack_notify | Posts notifications to Slack channels. | SlackMessage | PostResult<Channel> | token: String, channel: String | slack-api, reqwest |
| stripe_payment | Processes payments via Stripe API. | PaymentIntent | PaymentResult<Status> | api_key: String, webhook_secret: String | stripe, serde |
| twilio_sms | Sends SMS messages using Twilio. | SmsParams<To, Body> | SmsResult<Sid> | account_sid: String, auth_token: String | twilio, reqwest |
| github_api | Interacts with GitHub repos/APIs. | GitHubRequest | ApiResponse<RepoData> | token: String, org: String | octocrab, serde |
| aws_s3_upload | Uploads files to AWS S3 buckets. | S3Upload<File> | UploadResult<Etag> | bucket: String, region: String, access_key: String | aws-sdk-s3, tokio |
| sendgrid_email | Sends emails via SendGrid. | EmailContent | DeliveryResult<Id> | api_key: String, from: Email | sendgrid, reqwest |
| google_sheets | Reads/writes to Google Sheets. | SheetQuery | SheetData<Rows> | credentials: PathBuf, spreadsheet_id: String | google-sheets4, serde |

### AI/ML
These neurons leverage AI models for intelligent processing.

| Name | Description | Inputs | Outputs | Config Params | Dependencies |
|------|-------------|--------|---------|---------------|--------------|
| openai_generate | Generates text using OpenAI models. | Prompt<String> | Generation<Choices> | api_key: String, model: String, max_tokens: usize | openai, tokio |
| embedding_vector | Creates embeddings for text/search. | TextInput | Embedding<Vec<f32>> | api_key: String, model: String | openai, ndarray |
| content_moderator | Moderates content for safety. | ContentText | ModerationScore<Categories> | api_key: String, threshold: f64 | openai, serde |
| image_classifier | Classifies images using ML models. | ImageBytes | Classification<Labels> | model_path: PathBuf, top_k: usize | tract, image |
| sentiment_analyzer | Analyzes sentiment in text. | TextPayload | SentimentScore<Positive Neg> | model: String, lang: String | rust-bert, tokenizers |
| translation_service | Translates text between languages. | TranslationReq | TranslatedText | api_key: String, target_lang: String | google-cloud-translate, reqwest |

### Output/Responses
These neurons handle final outputs, writes, and notifications.

| Name | Description | Inputs | Outputs | Config Params | Dependencies |
|------|-------------|--------|---------|---------------|--------------|
| http_response | Sends HTTP responses back to clients. | ResponseData | HttpResponse<Status, Body> | status_code: u16, headers: Map | hyper, serde |
| db_commit | Commits transactions to databases. | TransactionData | CommitResult | conn: DbPool, isolation: Enum | sqlx, tokio |
| external_api_call | Makes outbound HTTP API calls. | ApiRequest<Url, Body> | ApiResponse<Status, Data> | timeout: Duration, auth: Option<Header> | reqwest, serde |
| file_exporter | Exports data to files (CSV/JSON). | ExportData | FilePath<Written> | format: Enum, path: PathBuf | csv, serde_json |
| email_sender | Sends emails via SMTP. | EmailDetails | SendResult | smtp_server: String, username: String, password: String | lettre, tokio |
| websocket_broadcast | Broadcasts messages to WebSocket clients. | BroadcastMsg | BroadcastStatus | room: String, exclude: Vec<Id> | tokio-tungstenite, serde |

### Utility (Additional Category: Logging, Encryption, Caching, etc.)
To ensure exhaustive coverage, this new category includes cross-cutting utilities.

| Name | Description | Inputs | Outputs | Config Params | Dependencies |
|------|-------------|--------|---------|---------------|--------------|
| logger | Logs events at configurable levels. | LogEvent | LogResult | level: Level, sink: PathBuf or Endpoint | tracing, serde |
| encrypt_data | Encrypts data with symmetric/asymmetric keys. | PlainBytes | EncryptedBytes | key: Key, mode: Enum | ring, serde |
| cache_manager | Manages in-memory or distributed caches. | CacheOp<Key, Value> | CacheResult | strategy: Enum, size_limit: usize | dashmap, redis |
| metric_collector | Collects and exports metrics (Prometheus). | MetricsData | ExportStatus | endpoint: String, interval: Duration | prometheus, tokio |
| pagination_applier | Paginates output streams. | Stream<Item> | PagedStream | page_size: usize, cursor: String | futures, serde |
| backup_creator | Creates backups of data states. | BackupConfig | BackupPath | storage: String, retention: Duration | tar, tokio::fs |
| audit_logger | Logs audit trails for compliance. | AuditEvent | AuditId | policy: String, store: DbUrl | tracing, sqlx |
| compression_handler | Compresses/decompresses data. | DataBytes | CompressedBytes | algorithm: Enum(Gzip), level: u8 | flate2, serde |
| uuid_generator | Generates unique identifiers. | Seed<Option<u64>> | Uuid | version: Enum(V4), namespace: Option<Uuid> | uuid, rand |
| timestamp_adder | Adds timestamps to data. | UntimedData | TimedData<Timestamp> | format: String, timezone: Tz | chrono, serde |

Total: 64 neurons. This list ensures full coverage, e.g., for chat-backend: oauth2_flow (Discord auth), postgres_query (users), websocket_listener/broadcast (chat), content_moderator (AI safety).

## NDK Details

The Neuron Development Kit (NDK) enables custom neuron creation, extending the system without core modifications. Structure: Templates in `/ndk/templates/{rust-neuron,python-neuron,ts-neuron}` with build scripts to WASM.

- **Supported Languages**:
  - Rust (primary): Direct WASM compilation via `wasm-bindgen` and `wasmtime`. Template includes `Cargo.toml` with `Neuron` trait shim.
  - Python: Via Pyodide/WasmEdge, wrapping Python code in Rust host for execution. Build: `maturin` to wheel, then embed in WASM module.
  - TypeScript: Using AssemblyScript or wasm-bindgen for JS interop. Build: `asc` compiler to WASM, with TS types for `Input/Output`.

- **Development Workflow**:
  1. Clone template: e.g., `cp -r /ndk/templates/rust-neuron /my-neuron`.
  2. Implement `Neuron` trait in `src/lib.rs`.
  3. Build: `wasm-pack build --target web` (outputs `pkg/my_neuron_bg.wasm`).
  4. Test locally: Integrate with Synapse Core's test harness.
  5. Package: ZIP with WASM, metadata JSON (name, version, inputs/outputs).

- **Sandboxing Mechanisms**:
  - Wasmtime config: `Config::new().wasm_threads(false).memory_limit(1024 * 1024 * 100)` (100MB limit), no filesystem access (`no_fs`), network disabled unless explicitly allowed.
  - Seccomp filters for syscalls (e.g., block `openat` for file ops).
  - Fallback to containerization (Docker) for non-WASM neurons, with resource limits (CPU: 1 core, Mem: 512MB).
  - Validation: Pre-execution hash checks and signature verification for marketplace downloads.

Custom neurons must declare dependencies in metadata, resolved at load time via core's plugin system.

## Integration with System

- **Loading/Registering Neurons**:
  - Dynamic via plugin system in `/backend/crates/core/src/plugins.rs`: Scan `/neurons/*.wasm`, instantiate with Wasmtime `Engine`, register in `NeuronRegistry<HashMap<Id, Box<dyn Neuron>>>`.
  - Configurable: JSON manifest loads params (e.g., `{ "name": "jwt_validate", "wasm_path": "...", "params": { "secret_key": "..." } }`).

- **Execution in Graph**:
  - Synapse Core graph runtime (`/backend/crates/core/src/graph.rs`) traverses pathways as DAGs: `async fn traverse(pathway: &Pathway, input: Input) -> Result<Output, SynapseError> { for neuron in pathway.nodes { let out = neuron.execute(input).await?; input = out; } }`.
  - Async with Tokio: Parallel branches via `tokio::spawn`, error propagation via `?` operator, timeouts per neuron.
  - State management: Shared `Arc<GraphState>` for context (e.g., user session).

- **Persistence in DB**:
  - `neurons` table (PostgreSQL): Columns: `id: Uuid PRIMARY KEY`, `name: String`, `code_wasm: Bytea`, `version: SemVer`, `price: Decimal`, `creator: Uuid`, `metadata: Json`.
  - Pathways store neuron refs by ID: `pathways` table with `neurons: Array<Uuid>`.

- **Marketplace Flows (The Cortex)**:
  - `/backend/crates/marketplace`: CRUD APIs for upload (validate WASM, scan for vulnerabilities), search (full-text on metadata), purchase (Stripe integration, 70/30 creator/platform split, credit tracking in user balances).
  - Download: Authenticated fetch, auto-validate signature/hash, install to local registry.
  - Versioning: Semantic versioning, rollback support, deprecation notices.
  - AI-Assisted Creation: Integrate with `openai_generate` neuron to suggest templates/code snippets based on natural language descriptions (e.g., "Create a neuron for SMS sending").

For chat-backend: Marketplace-sourced `oauth2_discord` + `ai_moderator` integrate seamlessly into graph.

## File-by-File Plans

Detailed blueprints for key files in `/backend/crates/neurons`, ensuring zero-error builds. All use Rust 1.75+, Cargo workspaces.

- **Cargo.toml**:
  ```
  [package]
  name = "synapse-neurons"
  version = "0.1.0"
  edition = "2021"

  [dependencies]
  wasmtime = "12.0"
  serde = { version = "1.0", features = ["derive"] }
  tokio = { version = "1.0", features = ["full"] }
  sqlx = { version = "0.7", features = ["runtime-tokio", "postgres"] }
  reqwest = { version = "0.11", features = ["json"] }
  jsonwebtoken = "9.0"
  tracing = "0.1"
  thiserror = "1.0"
  # Additional: async-graphql, tonic, mongodb, redis, openai, etc. per module
  ```

- **src/lib.rs**:
  ```
  use std::sync::Arc;
  use serde::{Deserialize, Serialize};
  use thiserror::Error;

  #[derive(Error, Debug)]
  pub enum SynapseError {
      #[error("Invalid token")]
      InvalidToken,
      #[error("Execution failed: {0}")]
      Execution(String),
      // More variants
  }

  pub type Input = serde_json::Value;
  pub type Output = serde_json::Value;

  pub trait Neuron: Send + Sync {
      fn execute(&self, input: Input) -> Result<Output, SynapseError>;
  }

  pub mod auth;
  pub mod db;
  pub mod processing;
  // More mods: input, integrations, ai, output, utility
  pub use auth::*;
  pub use db::*;
  // etc.
  ```

- **src/auth/jwt.rs** (Example for Auth Module):
  ```
  use super::*;
  use jsonwebtoken::{decode, DecodingKey, Validation};
  use std::sync::Arc;

  #[derive(Debug, Clone)]
  pub struct JwtNeuron {
      secret: Arc<String>,
      issuer: Arc<String>,
  }

  impl JwtNeuron {
      pub fn new(secret: String, issuer: String) -> Self {
          Self { secret: Arc::new(secret), issuer: Arc::new(issuer) }
      }
  }

  #[derive(Debug, Serialize, Deserialize)]
  pub struct Claims {
      sub: String,
      // More fields
  }

  impl Neuron for JwtNeuron {
      fn execute(&self, input: Input) -> Result<Output, SynapseError> {
          let token = input.get("token").and_then(|v| v.as_str()).ok_or(SynapseError::InvalidToken)?;
          let key = DecodingKey::from_secret(self.secret.as_bytes());
          let validation = Validation::new(jsonwebtoken::Algorithm::HS256);
          validation.set_issuer(&[self.issuer.as_str()]);
          let claims = decode::<Claims>(token, &key, &validation)
              .map_err(|_| SynapseError::InvalidToken)?;
          Ok(serde_json::json!({ "user": claims.claims.sub }))
      }
  }
  ```

- **src/auth/mod.rs**:
  ```
  pub mod jwt;
  pub mod oauth2;
  pub mod sso;
  // etc. for all auth neurons
  pub use jwt::JwtNeuron;
  // Exports
  ```

- **Similar for Other Modules**:
  - **src/db/postgres.rs**: Struct `PostgresNeuron { pool: sqlx::PgPool }`; impl `Neuron` with `sqlx::query!` execution, params from JSON input.
  - **src/processing/conditional_branch.rs**: Parse conditions as `Vec<(String, bool, String)>`, evaluate with `eval::eval`, return branch path.
  - **src/integrations/stripe.rs**: Use `stripe::Client`, call `PaymentIntent::create` with input params.
  - **src/ai/openai_generate.rs**: `openai::Client::new()`, `chat::ChatCompletion::new()` with streaming support.
  - **src/utility/logger.rs**: `tracing::info_span!`, configurable sink to file or console.

- **tests/jwt_test.rs** (For Each Neuron):
  ```
  #[cfg(test)]
  mod tests {
      use super::*;
      use serde_json::json;

      #[tokio::test]
      async fn test_jwt_valid() {
          let neuron = JwtNeuron::new("secret".to_string(), "synapse".to_string());
          let input = json!({ "token": "valid_jwt_here" });
          let output = neuron.execute(input).unwrap();
          assert_eq!(output["user"], "test_user");
      }

      #[tokio::test]
      async fn test_jwt_invalid() {
          let neuron = JwtNeuron::new("secret".to_string(), "synapse".to_string());
          let input = json!({ "token": "invalid" });
          assert!(neuron.execute(input).is_err());
      }
  }
  ```
  Full unit/integration tests: 80% coverage, mock inputs, assert outputs/errors. Integration: Mock Wasmtime for WASM neurons.

- **Marketplace Crate (/backend/crates/marketplace/src/lib.rs)**: `pub struct Cortex { db: DbPool, stripe: StripeClient }`; Methods: `upload_neuron(wasm: Vec<u8>, metadata: NeuronMeta) -> Uuid`, `search(query: String) -> Vec<NeuronInfo>`, `purchase(id: Uuid, user_id: Uuid) -> Transaction`.

Build: `cargo build --release`, zero errors via CI checks (clippy, tarpaulin).

## Security & Risks

- **Input Validation**: All neurons use `serde` with `#[serde(deny_unknown_fields)]` and `validator` crate for schema checks (e.g., `#[validate(length(min=1))]`). Custom validators in processing neurons.
- **Anomaly Detection**: Dedicated `anomaly_detector` neuron uses ML (linfa clustering) on request patterns; integrates with rate_limiter for auto-throttling. Global: Audit logs in every pathway.
- **Versioning Conflicts**: Semantic versioning enforced; runtime checks `version.compatible_with(required)`, fallback to latest compatible.
- **Custom Neuron Safety**: Sandboxing prevents escapes (Wasmtime pool isolation, no shared memory). Pre-upload scans with `cargo-audit` and WASM validator. Risks: Supply-chain attacks mitigated by signature verification; memory leaks via limits.
- **Other Risks**: DoS via infinite loops (max_iterations in loop_iterator); data exfiltration (network disabled in sandbox). Mitigation: Full audit crate integration for compliance.

## Mermaid Diagrams

### Neuron Execution Flow
```mermaid
graph TD
    A[Input Request] --> B[Graph Runtime Traverse]
    B --> C{Load Neuron}
    C -->|Dynamic Registry| D[Instantiate WASM]
    D --> E[Execute Neuron]
    E --> F{Error?}
    F -->|Yes| G[Error Handler Neuron]
    F -->|No| H[Output to Next]
    H --> I[Async Branch/Join]
    I --> J[Final Output]
    G --> J
    style E fill:#f9f,stroke:#333
```

### Category Overview Graph
```mermaid
graph LR
    Input[Input/Entry Points<br/>8 Neurons]
    Auth[Auth/Security<br/>7 Neurons]
    DB[Storage/DB<br/>7 Neurons]
    Proc[Processing<br/>7 Neurons]
    Int[Integrations<br/>8 Neurons]
    AI[AI/ML<br/>6 Neurons]
    Out[Output<br/>6 Neurons]
    Util[Utility<br/>10 Neurons]
    Input --> Auth --> DB --> Proc --> Int --> AI --> Out --> Util
    Util -.-> Input
    style Input fill:#e1f5fe
    style Auth fill:#f3e5f5
    style DB fill:#e8f5e8
```

### NDK Build Pipeline
```mermaid
sequenceDiagram
    participant Dev
    participant Template
    participant Builder
    participant Validator
    participant Registry
    Dev->>Template: Clone rust-neuron
    Dev->>Builder: Implement & Build to WASM
    Builder->>Validator: Scan & Sign
    Validator->>Registry: Upload to Cortex
    Registry->>Dev: Neuron ID
    Note over Dev,Registry: Package ZIP + Metadata
```

This plan serves as the source of truth for implementation, covering 64 neurons across 8 categories, NDK with 3-language support and sandboxing, full integration details, file blueprints, and security measures.