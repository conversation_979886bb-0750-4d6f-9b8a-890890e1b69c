# Synapse-IO Frontend (Synapse Studio) Planning (Revised)

## Introduction

Synapse-IO Frontend, branded as Synapse Studio, serves as the intuitive drag-and-drop AI IDE for designing, building, and deploying neuron-based pathway graphs. It targets beginners and enterprises, enabling plain English AI prompts to generate complex workflows while providing enterprise-grade testing, monitoring, and secure deployment. The application emphasizes zero-error production readiness, full offline capabilities, and closed-source security measures.

### Role and Objectives
- **Primary Role**: Visual canvas for neuron orchestration with live collaboration, AI-assisted design, and marketplace integration for neuron discovery/purchase.
- **Key Objectives**:
  - Deliver beginner-friendly UX with schema-driven forms replacing raw JSON editing.
  - Ensure seamless offline editing with automatic sync on reconnect, including conflict resolution.
  - Achieve WCAG AA+ accessibility with ARIA announcements for errors and recovery states.
  - Support mobile/PWA deployment for on-the-go pathway design with touch gestures.
  - Align with backend architecture: Axum/tungstenite for REST/WS, AI crate integrations, JWT-secured endpoints.

### Tech Stack
- **Framework**: React 18+ with TypeScript (strict mode), Vite for fast bundling and HMR.
- **State Management**: Zustand with persistence middleware (localStorage/IndexedDB fallback) for offline graph state.
- **Routing**: React Router v6 for SPA navigation (e.g., /canvas, /marketplace, /deploy).
- **UI Library**: Ant Design (antd) for components, Tailwind CSS for custom styling (with overrides for react-flow nodes).
- **Canvas**: React Flow for drag-drop graph visualization, enhanced with virtualization for large graphs and mini-map.
- **Forms/Validation**: react-hook-form with Zod resolver for schema-driven input sanitization (e.g., neuron configs, AI prompts).
- **Offline/PWA**: Vite PWA plugin for service worker, IndexedDB (via idb-keyval or Dexie) for local storage of pathway graphs with conflict resolution (last-write-wins or merge UI).
- **WebSocket**: socket.io-client with custom useWebSocket hook for reconnect (exponential backoff, max 5 attempts, heartbeat pings every 30s).
- **HTTP**: Axios for REST CRUD, with Zod parsing for response validation.
- **Gestures/Mobile**: react-use-gesture for touch handling on react-flow canvas (pinch-zoom, drag-drop).
- **Testing**: Vitest/RTL for unit/integration (95% coverage), Cypress for e2e workflows (login to deploy).
- **Error Handling**: React ErrorBoundary components with fallback UI, recovery from local storage.
- **Security**: CSP headers via Vite config, Zod for all inputs, httpOnly cookies for JWT (proxied via backend auth), no localStorage for sensitive data.
- **Other**: React Query for caching (optional for marketplace), Sentry proxy for error logging.

### Design Principles
- **Beginner UX**: Schema forms with tooltips, auto-complete for neuron params; AI panel with prioritized suggestions to avoid overwhelm.
- **Production Readiness**: No placeholders/mocks; full functionality with offline sync, error recovery, and loading states.
- **Modularity**: Feature-sliced structure (/src/features, /src/shared), barrel exports for services/hooks.
- **Alignment**: Matches Overview.txt (portable PWA, intuitive IDE) and architecture (modular crates, secure WS/REST).

## UI/UX Architecture

### User Journeys
- **Onboarding**: Login (JWT via backend proxy) -> Dashboard with recent pathways -> Canvas for new design.
- **Design Flow**: Drag neurons from palette -> Connect edges -> Configure via Zod-validated forms -> AI prompt for auto-generation -> Preview export.
- **Collaboration**: Real-time WS sync; offline mode: edit locally -> indicator shows "Offline: Saving to device" -> on reconnect: sync with conflict UI (e.g., modal for merge choices).
- **Marketplace**: Browse/purchase neurons -> Optimistic UI update -> Error toast on failure.
- **Deploy/Monitor**: Validate graph (Zod schema) -> Deploy button -> Monitoring dashboard with live metrics.
- **Offline Mode**: Seamless transition on WS drop; local edits persist; retry sync with progress bar; fallback to read-only if conflicts unresolvable.
- **Mobile Journey**: PWA install prompt; touch drag-drop on canvas with gesture feedback; responsive sidebar collapse.

### Accessibility Enhancements
- ARIA live regions for sync status (e.g., "Pathway recovered from local storage").
- Keyboard navigation for react-flow (arrow keys for node selection).
- Screen reader support for form errors (Zod integrates with aria-invalid).
- High-contrast mode via Tailwind, WCAG AA+ color ratios.

### Updated Mermaid Diagrams

#### User Workflow with Offline Sync (Sequence Diagram)
```mermaid
sequenceDiagram
    participant U as User
    participant FE as Frontend
    participant SW as Service Worker
    participant IDB as IndexedDB
    participant BE as Backend
    participant WS as WebSocket

    U->>FE: Edit Pathway (Drag/Drop)
    FE->>WS: Send Update
    Note over WS,BE: Connection Drops
    FE->>SW: Queue Update
    SW->>IDB: Store Local (with Timestamp)
    FE->>U: Show Offline Indicator
    U->>FE: Continue Editing
    FE->>SW: Queue More Updates
    Note over SW,IDB: Conflict Detection (Timestamps)
    WS->>FE: Reconnect (Backoff Retry)
    FE->>SW: Flush Queue
    alt No Conflicts
        SW->>BE: Sync Updates
        BE->>SW: Ack Success
    else Conflicts
        SW->>U: Show Merge Modal
        U->>SW: Resolve (Last-Write-Wins)
        SW->>BE: Sync Resolved
    end
    FE->>U: Sync Complete Toast
```

#### Error Handling Flow
```mermaid
graph TD
    A[Normal Operation] --> B{WS/Sync Error?}
    B -->|Yes| C[Trigger ErrorBoundary]
    C --> D[Show Fallback UI: 'Pathway recovered from local storage']
    D --> E[Log to Console/Sentry]
    E --> F[Attempt Recovery: Load from IndexedDB]
    F --> G{Recovery Success?}
    G -->|Yes| H[Retry Sync with Backoff]
    H --> I{Retry Success?}
    I -->|Yes| A
    I -->|No (Max 5)| J[Show Permanent Fallback: Read-Only Mode]
    G -->|No| J
    B -->|No| A
    J --> K[User Action: Manual Refresh or Report]
```

## Component Structure

Enhanced hierarchy with error resilience and offline support. All components use TypeScript, memoization for perf.

- **App.tsx** (Root):
  - Providers: BrowserRouter, Zustand Provider (with persist), QueryClientProvider.
  - PWA registration: Use Vite PWA plugin to register service worker.
  - Global ErrorBoundary wrapper.
  - Routes: /canvas (protected), /marketplace, /login.
  - Layout: Sidebar (Palette/AI Panel), Main Content, Footer (status: online/offline).

- **CanvasPage.tsx** (Core Canvas):
  - React Flow canvas with nodes/edges from Zustand store.
  - ErrorBoundary for react-flow crashes (e.g., invalid node data) -> Fallback: Blank canvas with "Recovering..." and load from local.
  - Offline indicator (badge) and sync button.
  - Mini-map for large graphs, virtualization via react-window.
  - Mobile: react-use-gesture for touch events (drag node, pinch zoom).

- **NeuronPalette.tsx** (Sidebar Palette):
  - Draggable list of 74 neurons (icons/descriptions).
  - Categories: Core, AI, Integration, Enterprise.
  - Search/filter with debounce.

- **NeuronNode.tsx** (Node Editor):
  - Modal/panel on node select.
  - react-hook-form with ZodResolver: Schema e.g., `z.object({ name: z.string().min(1), secret_key: z.string().min(32).optional(), params: z.record(z.any()) })`.
  - Form fields: Inputs, selects, toggles based on schema; validation errors with Antd feedback.
  - Auto-save to Zustand/IndexedDB on change.
  - No raw JSON; inferred type-safe UI from Zod.

- **AI Panel.tsx** (AI Assistant Sidebar):
  - Input for plain English prompts (Zod validated: z.string().max(500)).
  - Loading spinner for LLM calls (30s timeout).
  - Prioritized suggestions: Top 3 graph modifications, with preview.
  - History of prompts/responses, collapsible to avoid overwhelm.

- **MarketplacePage.tsx** (Neuron Marketplace):
  - Grid of neuron cards (name, desc, price, preview).
  - Purchase flow: Button -> Loading state -> Optimistic add to palette -> Error toast if fail.
  - Zod for search/query params.

- **ErrorBoundary.tsx** (Reusable):
  - ComponentClass with static getDerivedStateFromError.
  - Fallback UI: Antd Alert with "Something went wrong. Recovering pathway..." and retry button.
  - Recovery: Dispatch Zustand action to load from persist store.
  - Logs error to console (Sentry integration optional).

- **Other**: LoginForm.tsx (with Zod), DeployModal.tsx (validation before send), MonitoringDashboard.tsx (live metrics via WS).

### Updated Mermaid: Component Tree
```mermaid
graph TD
    App[App.tsx<br/>- Providers<br/>- PWA Register<br/>- ErrorBoundary]
    App --> CanvasPage[CanvasPage.tsx<br/>- React Flow<br/>- ErrorBoundary<br/>- Offline Indicator<br/>- useGesture]
    App --> Marketplace[MarketplacePage.tsx<br/>- Neuron Grid<br/>- Purchase Flow]
    App --> Auth[Auth Routes<br/>- LoginForm.tsx<br/>- Zod Validation]
    CanvasPage --> Palette[NeuronPalette.tsx<br/>- Draggable List]
    CanvasPage --> NodeEditor[NeuronNode.tsx<br/>- react-hook-form<br/>- ZodResolver<br/>- Auto-save IDB]
    CanvasPage --> AIPanel[AIPanel.tsx<br/>- Prompt Input<br/>- Loading States<br/>- Suggestions]
    NodeEditor -.-> ErrorBoundary
    AIPanel -.-> ErrorBoundary
    App -.-> useWebSocket[Custom Hook<br/>- useWebSocket.ts<br/>- Reconnect Backoff<br/>- Heartbeat]
```

## State Management & Data Flows

### Global State (Zustand)
- Store: `pathwayStore` with slices for graph (nodes/edges), user (JWT), ui (selectedNode, offline).
- Persistence: Middleware to IndexedDB (key: 'synapse-graph', versioned).
- Offline: Mutations queue in IDB; on reconnect, resolve conflicts (timestamp-based last-write-wins, or UI merge for edges).
- Selectors: `usePathwayGraph`, `useIsOffline`, `useSyncStatus`.

### Local State
- React Flow: Internal state for viewport, selection; gesture-enhanced for mobile.
- Forms: react-hook-form local state, synced to global on submit.

### Data Flows
- **Sync**: WS for live collab (useWebSocket hook: connect on mount, exponential backoff 1s-32s, ping/pong heartbeat).
- **REST**: Axios intercepts for auth (JWT header), Zod.parse on responses (e.g., pathwayService.getAll() -> z.array(schema)).
- **Offline Branch**: On disconnect, route mutations to IDB queue; flush on reconnect with retry.
- **AI Flow**: Prompt -> aiService.post() -> Zod-validated graph delta -> Apply to store.

### Updated Mermaid: Data Flow with Offline
```mermaid
graph LR
    UI[UI Components<br/>e.g., Drag Node] --> Store[Zustand Store<br/>Persist Middleware]
    Store --> WS[useWebSocket Hook<br/>- Send Update<br/>- Heartbeat Ping]
    WS --> BE[Backend<br/>Axum/WS]
    Store --> REST[Axios + Zod Parse<br/>e.g., CRUD Pathways]
    REST --> BE
    WS -.->|Disconnect| Offline[Offline Mode<br/>Queue to IndexedDB]
    Offline --> Conflict[Conflict Resolver<br/>Last-Write-Wins / Merge UI]
    Conflict --> Store
    BE --> WS
    BE --> REST
    AI[AI Prompt] --> AIsvc[aiService.ts<br/>Timeout 30s, Zod Schema]
    AIsvc --> Store
    Mkt[Purchase] --> CortexSvc[cortexService.ts<br/>Zod for Neurons, Loading]
    CortexSvc --> Store
```

## Integrations

### Backend Integrations
- **pathwayService.ts** (REST/WS):
  - CRUD: getAll, create, update, delete with Zod schemas (e.g., response: z.array(z.object({id: z.string(), nodes: z.array(...)}))).
  - Offline: Queue mutations in IDB (e.g., {type: 'updateNode', data: {...}, timestamp: Date.now()}).
  - Sync: On reconnect, batch send queued items, handle 409 conflicts with merge.

- **websocketService.ts** / useWebSocket.ts Hook:
  - Custom hook: useEffect for connection, state: {connected, retrying, error}.
  - Reconnect: Exponential backoff (delay = Math.min(2 ** attempt * 1000, 32000)), max 5 attempts.
  - Events: 'pathway-update' (broadcast graph changes), 'heartbeat' (ping every 30s, pong timeout 10s).
  - Auth: JWT in socket handshake.

### AI Integrations
- **aiService.ts**:
  - POST /ai/generate: Prompt -> LLM (via backend crate), timeout 30s with AbortController.
  - Response: Zod schema for graph JSON (z.object({nodes: z.array(...), edges: ...})).
  - Error: Fallback to cached suggestions or offline mode.

### Marketplace Integrations
- **cortexService.ts**:
  - GET /neurons: List with Zod (z.array(z.object({id, name, price, configSchema: z.object()}))).
  - POST /purchase: With loading state, optimistic palette update on 200.
  - Error: Antd toast, rollback optimism.

### Security Measures
- **CSP**: Vite config: `contentSecurityPolicy: { directives: { scriptSrc: ["'self'"], ... } }`; meta in index.html.
- **Input Sanitization**: Zod for all forms/APIs (e.g., no unsanitized JSON).
- **Auth**: Prefer httpOnly cookies (set via backend proxy on login); fallback to secure localStorage only if needed. WS upgrade with JWT.
- **Other**: No eval/innerHTML; react-hook-form prevents injection; closed-source: minify/obfuscate in prod.

## File-by-File Plans

### Configuration Files
- **package.json**:
  - Dependencies: react, react-dom, @types/react, typescript, vite, react-router-dom, zustand, react-flow, antd, tailwindcss, socket.io-client, axios, @hookform/resolvers/zod, react-hook-form, zod, vite-plugin-pwa, react-use-gesture, cypress, @testing-library/react, vitest, @testing-library/cypress.
  - Dev: @types/node, tsconfig strict, postcss, autoprefixer.
  - Scripts: "dev": "vite", "build": "vite build", "test": "vitest", "cypress": "cypress run", "e2e": "start-server-and-test dev http://localhost:5173 cypress".

- **tsconfig.json**:
  - Strict: true, noImplicitAny: true, skipLibCheck: true.
  - Paths: "@/*": ["src/*"], "@components/*": ["src/components/*"].

- **vite.config.ts**:
  - Plugins: react(), vitePWA({ registerType: 'autoUpdate', workbox: { globPatterns: ['**/*.{js,css,html,ico,png}'] }, manifest: { name: 'Synapse Studio', icons: [...] } }).
  - CSP: defineConfig({ preview: { headers: { 'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'" } } }).
  - Resolve: aliases for src.

- **tailwind.config.js**: Extend antd theme, custom colors for neurons, responsive breakpoints.
- **postcss.config.js**: tailwindcss, autoprefixer.

### Source Files
- **src/main.tsx**: Render App with StrictMode, hydrate for SSR if needed.
- **src/hooks/useWebSocket.ts**:
  - Hook: const [state, setState] = useState({connected: false, retrying: false});
  - useEffect: socket = io(url, {auth: {token: jwt}}); handle connect/disconnect/reconnect with backoff logic (attempt counter, setTimeout).
  - Heartbeat: setInterval ping, on pong reset timeout.
  - Return: {socket, state, sendUpdate}.

- **src/components/ErrorBoundary.tsx**:
  - class ErrorBoundary extends React.Component: componentDidCatch(error, info) { console.error(error); /* sentry */ }, getDerivedStateFromError() { return {hasError: true}; }.
  - Render: hasError ? <Alert message="Pathway recovered" type="warning"><Button onClick={reset}>Retry</Button></Alert> : children.
  - Reset: Dispatch loadFromPersist, setState({hasError: false}).

- **src/components/NeuronNode.tsx**:
  - useForm({resolver: zodResolver(schema)}), where schema = z.object({name: z.string().min(1), secret_key: z.string().min(32).optional(), ...}).
  - <FormProvider {...methods}><form onSubmit={handleSubmit(onSave)}>{/* Dynamic fields from schema */}</form></FormProvider>.
  - onSave: Update store, queue to IDB if offline.
  - watch('secret_key') for validation feedback.

- **src/services/pathwayService.ts**:
  - Export functions: async getAll() { const res = await axios.get('/pathways'); return z.array(pathwaySchema).parse(res.data); }.
  - update(id, data) { z.object({nodes: z.array(...)}).parse(data); return offline ? queueMutation(...) : axios.put(...); }.
  - syncQueue(): Batch queued items, resolve conflicts.

- **src/services/aiService.ts**:
  - async generate(prompt: string) { const controller = new AbortController(); setTimeout(() => controller.abort(), 30000); const res = await axios.post('/ai/generate', {prompt}, {signal: controller.signal}); return graphSchema.parse(res.data); }.

- **src/services/cortexService.ts**:
  - async getNeurons() { const res = await axios.get('/neurons'); return z.array(neuronSchema).parse(res.data); }.
  - async purchase(id) { /* optimistic update */ const res = await axios.post('/purchase', {id}); /* Zod parse */ }.

- **cypress.config.ts**:
  - e2e: { baseUrl: 'http://localhost:5173', setupNodeEvents(on, config), supportFile: 'cypress/support/e2e.ts' }.
  - viewport: {width: 1280, height: 720}.

- **cypress/e2e/workflow.cy.ts**:
  - describe('Full Workflow', () => {
    it('designs and deploys pathway', () => {
      cy.visit('/canvas'); // Assumes login fixture
      cy.get('[data-cy=drag-neuron]').trigger('dragstart', {clientX: 100, clientY: 100}).trigger('drop', {clientX: 300, clientY: 300}); // Drag to canvas
      cy.get('[data-cy=node-config]').within(() => { cy.get('input[name=name]').type('Test Neuron'); cy.get('input[type=submit]').click(); });
      cy.get('[data-cy=ai-prompt]').type('Add connection'); cy.get('button[type=submit]').click();
      cy.get('[data-cy=deploy]').click(); // Assert success toast
      cy.url().should('include', '/dashboard');
    });
  });

- **Tests**: vitest.config.ts for RTL; coverage 95% (units for hooks/services, integration for components, e2e for workflows). No mocks; real IndexedDB in tests via msw.

### Other Files
- **src/store/pathwayStore.ts**: create((set, get) => ({ graph: [], isOffline: false, persistConfig: {name: 'synapse-storage'} })); actions for updateGraph, toggleOffline, resolveConflict.
- **public/manifest.json**: PWA icons, theme_color.
- **src/utils/validationSchemas.ts**: Export all Zod schemas (pathway, neuron, prompt).

## Performance & Accessibility

### Optimizations
- **PWA Caching**: Service worker precaches assets/neurons; runtime caching for API responses (stale-while-revalidate).
- **Graph Perf**: React Flow virtualization for 1000+ nodes; mini-map LOD.
- **Gestures**: react-use-gesture reduces touch lag (throttle events); no polyfill needed for modern browsers.
- **Bundle**: Vite tree-shaking, lazy-load routes (React.lazy for Marketplace).

### Accessibility
- **Error Recovery**: ErrorBoundary announces via aria-live="polite": "Pathway recovered from local storage. Retrying sync...".
- **Forms**: Zod errors map to aria-describedby; react-hook-form supports screen readers.
- **Canvas**: aria-label on nodes/edges; keyboard focus outline for selection.
- **Mobile**: Touch targets 48px min; PWA install prompt with a11y.

### Mobile Enhancements
- Responsive: Tailwind mobile-first (sidebar collapses to drawer).
- Gestures: Pinch for zoom, long-press for node menu; drag-drop with haptic feedback if supported.
- PWA: Offline install, background sync for deploys.

## Risks/Gaps & Mitigations

- **WS Reconnect**: Detailed backoff algo in useWebSocket; test with simulated drops in Cypress.
- **Offline Conflicts**: Last-write-wins for nodes, merge UI for edges (modal with diff view); persist user choice.
- **Large Graphs**: React Flow mini-map + virtualization; lazy-load node details.
- **AI Latency**: 30s timeout, spinners, fallback to local templates.
- **Security**: CSP blocks XSS; Zod prevents injection; audit httpOnly JWT proxy feasibility with backend.
- **Testing Gaps**: 95% unit + full e2e; no untested offline paths.
- **Feasibility**: All libs mature; no custom WS races (use socket.io guarantees); mobile tested via Cypress mobile viewport.