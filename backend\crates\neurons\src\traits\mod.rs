use serde::{Deserialize, Serialize};

use synapse_core::{Output, SynapseError};

#[async_trait::async_trait]
pub trait Neuron: Send + Sync {
    fn name(&self) -> &'static str;
    fn description(&self) -> &'static str;
    fn version(&self) -> &'static str;
    fn input_schema(&self) -> Option<&'static str> {
        None
    }
    fn output_schema(&self) -> Option<&'static str> {
        None
    }
    fn config_schema(&self) -> Option<&'static str> {
        None
    }
    async fn execute(&self, ctx: &mut synapse_core::ExecutionContext) -> Result<Output, SynapseError>;
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct NeuronMetadata {
    pub name: String,
    pub description: String,
    pub version: String,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::Neuron;
    use synapse_core::{Input, ExecutionContext};
    use std::collections::HashMap;
    use serde_json::Value;
    use chrono;

    struct TestNeuron;

    #[async_trait::async_trait]
    impl Neuron for TestNeuron {
        fn name(&self) -> &'static str {
            "test_neuron"
        }

        fn description(&self) -> &'static str {
            "Test neuron for validation"
        }

        fn version(&self) -> &'static str {
            "1.0.0"
        }

        async fn execute(&self, ctx: &mut ExecutionContext) -> Result<Output, SynapseError> {
            if let Some(input_data) = ctx.input.data.get("test_input") {
                if let Some(input_str) = input_data.as_str() {
                    ctx.output.data = serde_json::json!({
                        "processed": format!("Processed: {}", input_str),
                        "timestamp": chrono::Utc::now().to_rfc3339()
                    });
                    ctx.output.status = 200;
                    return Ok(ctx.output.clone());
                }
            }
            Err(SynapseError::InvalidInput("Missing or invalid test_input".to_string()))
        }
    }

    #[tokio::test]
    async fn test_neuron_execution() {
        let input_data = HashMap::from([
            (
                "test_input".to_string(),
                Value::String("Hello Synapse!".to_string()),
            ),
        ]);

        let mut context = ExecutionContext::new(Input {
            data: input_data,
            ..Default::default()
        });

        let neuron = TestNeuron;
        let result = neuron.execute(&mut context).await;

        assert!(result.is_ok());
        let output = result.unwrap();
        assert_eq!(output.status, 200);
        assert!(output.data.is_object());
        assert!(output.data.get("processed").is_some());
        assert!(output.data.get("timestamp").is_some());
    }

    #[tokio::test]
    async fn test_neuron_validation_error() {
        let input_data = HashMap::new();
        // Missing test_input

        let mut context = ExecutionContext::new(Input {
            data: input_data,
            ..Default::default()
        });

        let neuron = TestNeuron;
        let result = neuron.execute(&mut context).await;

        assert!(result.is_err());
        if let Err(SynapseError::InvalidInput(msg)) = result {
            assert!(msg.contains("test_input"));
        } else {
            panic!("Expected InvalidInput error");
        }
    }

    #[test]
    fn test_neuron_metadata() {
        let neuron = TestNeuron;
        
        assert_eq!(neuron.name(), "test_neuron");
        assert_eq!(neuron.description(), "Test neuron for validation");
        assert_eq!(neuron.version(), "1.0.0");
    }
}