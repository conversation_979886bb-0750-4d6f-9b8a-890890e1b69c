[package]
name = "synapse-neurons"
version = "0.1.0"
edition = "2021"

[dependencies]
futures = "0.3"
synapse-core = { path = "../core" }
async-trait = "0.1"
jsonwebtoken = "9.3"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.7", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
tokio = { version = "1.0", features = ["full"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "sqlite", "mysql", "migrate", "uuid", "chrono"] }
redis = { version = "0.24", features = ["aio", "tokio-comp", "connection-manager"] }
mongodb = { version = "2.8", features = ["tokio-runtime"] }
tempfile = "3.10"
reqwest = { version = "0.11", features = ["json"] }
base64 = "0.22"
futures-util = "0.3"

[dev-dependencies]
proptest = "1.7"