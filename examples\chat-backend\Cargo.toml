[package]
name = "chat-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
synapse-core = { path = "../../backend/crates/core" }
synapse-neurons = { path = "../../backend/crates/neurons" }
synapse-api = { path = "../../backend/crates/api" }
synapse-db = { path = "../../backend/crates/db" }
tokio.workspace = true
axum.workspace = true
actix.workspace = true
sqlx.workspace = true
serde.workspace = true
serde_json.workspace = true
jsonwebtoken.workspace = true
tracing.workspace = true