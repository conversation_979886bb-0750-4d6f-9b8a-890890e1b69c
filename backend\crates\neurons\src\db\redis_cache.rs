use redis::{AsyncCommands, RedisError, aio::ConnectionManager};
use serde_json::Value;
use std::time::Duration;
use tokio::time::{sleep, timeout};
use crate::Neuron;
use synapse_core::{ExecutionContext, Input, Output, SynapseError};
use uuid::Uuid;
use redis::pipe;
use redis::RedisErrorKind;

#[derive(Clone)]
pub struct RedisCache {
    manager: ConnectionManager<redis::Client>,
    ttl_secs: u64,
}

impl RedisCache {
    pub async fn new(redis_url: &str, ttl_secs: u64) -> Result<Self, SynapseError> {
        let client = redis::Client::open(redis_url).map_err(SynapseError::Database)?;
        let manager = ConnectionManager::new(client).await.map_err(SynapseError::Database)?;
        Ok(Self { manager, ttl_secs })
    }
}

impl Neuron for RedisCache {
    fn name(&self) -> &'static str {
        "redis_cache"
    }

    fn description(&self) -> &'static str {
        "Redis cache get/set with TTL support"
    }

    fn version(&self) -> u32 {
        1
    }

    fn input_schema(&self) -> Option<&'static str> {
        Some(r##"{"type":"object","properties":{"action":{"type":"string","enum":["get","set"]},"key":{"type":"string","description":"Cache key with tenant prefix"},"value":{"type":"string","description":"Value for set action"},"ttl":{"type":"integer","description":"Override default TTL, uses self.ttl_secs if not provided"}},"required":["action","key"],"if":{"properties":{"action":{"const":"set"}},"then":{"required":["value"]}}}"##)
    }

    async fn execute(&self, ctx: &mut ExecutionContext) -> Result<Output, SynapseError> {
        fn is_transient(err: &RedisError) -> bool {
            matches!(err.kind(), RedisErrorKind::IoError | RedisErrorKind::ExtensionError | RedisErrorKind::Timeout)
        }

        fn map_error(err: RedisError) -> SynapseError {
            match err.kind() {
                RedisErrorKind::IoError => SynapseError::DbConnectionLost,
                RedisErrorKind::Timeout => SynapseError::RedisTtlExpired,
                _ => SynapseError::Database(err.to_string()),
            }
        }

        let action = ctx.input.data.get("action").and_then(|v| v.as_str()).ok_or(SynapseError::InvalidInput("Missing action".into()))?;
        let key = ctx.input.data.get("key").and_then(|v| v.as_str()).ok_or(SynapseError::InvalidInput("Missing key".into()))?;
        let tenant_id = ctx.tenant_id.unwrap_or(Uuid::nil());
        let prefixed_key = format!("tenant:{}:{}", tenant_id, key);
        if prefixed_key.len() > 250 {
            return Err(SynapseError::InvalidInput("Prefixed key too long (max 250 chars)".into()));
        }

        let mut con = {
            let mut con_opt = None;
            for attempt in 1..=3u32 {
                match self.manager.get_connection().await {
                    Ok(c) => {
                        con_opt = Some(c);
                        break;
                    }
                    Err(err) => {
                        if attempt == 3 || !is_transient(&err) {
                            return Err(map_error(err));
                        }
                        sleep(Duration::from_millis(100 * attempt as u64)).await;
                    }
                }
            }
            con_opt.expect("Connection should be established after retries")
        };

        match action {
            "get" => {
                let value: Option<String> = {
                    let mut value_opt = None;
                    for attempt in 1..=3u32 {
                        match timeout(Duration::from_secs(30), con.get(&prefixed_key)).await {
                            Ok(Ok(v)) => {
                                value_opt = Some(v);
                                break;
                            }
                            Ok(Err(err)) => {
                                if attempt == 3 || !is_transient(&err) {
                                    return Err(map_error(err));
                                }
                                sleep(Duration::from_millis(100 * attempt as u64)).await;
                            }
                            Err(_) => {
                                let timeout_err = RedisError::from((RedisErrorKind::Timeout, "get operation timeout"));
                                if attempt == 3 || !is_transient(&timeout_err) {
                                    return Err(map_error(timeout_err));
                                }
100|                                 sleep(Duration::from_millis(100 * attempt as u64)).await;
101|                             }
102|                         }
                    }
                    value_opt.expect("Value should be retrieved after retries")
                };
                let size_bytes = if let Some(ref val) = value { val.len() as u64 } else { 0 };
                let ttl_remaining = if value.is_some() {
                    con.ttl(&prefixed_key).await.map_err(map_error)?.map(|t| t as u64)
                } else {
                    None
                };
                ctx.output.data = if let Some(val) = value {
                    Value::String(val)
                } else {
                    Value::Null
                };
                ctx.output.status = 200;
                ctx.output.metadata.insert("cached".to_string(), Value::Bool(true));
                ctx.output.metadata.insert("atomic_success".to_string(), Value::Bool(true));
                ctx.output.metadata.insert("size_bytes".to_string(), Value::Number(size_bytes.into()));
                if let Some(ttl) = ttl_remaining {
                    ctx.output.metadata.insert("ttl_remaining".to_string(), Value::Number(ttl.into()));
                } else {
                    ctx.output.metadata.insert("ttl_remaining".to_string(), Value::Null);
                }
            }
            "set" => {
                let value = ctx.input.data.get("value").and_then(|v| v.as_str()).ok_or(SynapseError::InvalidInput("Missing value".into()))?;
                if value.len() > 1_048_576 {
                    return Err(SynapseError::InvalidInput("Value too large (max 1MB)".into()));
                }
                let ttl = ctx.input.data.get("ttl").and_then(|v| v.as_u64()).unwrap_or(self.ttl_secs);
                // Atomic set with expire using MULTI/EXEC pipeline
                let mut pipe = redis::pipe();
                pipe.atomic().set(&prefixed_key, value).ignore();
                if ttl > 0 {
                    pipe.atomic().expire(&prefixed_key, ttl).ignore();
                }
                {
                    for attempt in 1..=3u32 {
                        match timeout(Duration::from_secs(30), pipe.query_async(&mut con)).await {
                            Ok(Ok(_)) => break,
                            Ok(Err(err)) => {
                                if attempt == 3 || !is_transient(&err) {
                                    return Err(map_error(err));
                                }
                                sleep(Duration::from_millis(100 * attempt as u64)).await;
                            }
                            Err(_) => {
                                let timeout_err = RedisError::from((RedisErrorKind::Timeout, "set operation timeout"));
                                if attempt == 3 || !is_transient(&timeout_err) {
                                    return Err(map_error(timeout_err));
                                }
                                sleep(Duration::from_millis(100 * attempt as u64)).await;
                            }
                        }
                    }
                }
                ctx.output.data = Value::String("OK".to_string());
                ctx.output.status = 200;
                ctx.output.metadata.insert("ttl_remaining".to_string(), Value::Number(ttl.into()));
                ctx.output.metadata.insert("atomic_success".to_string(), Value::Bool(true));
                ctx.output.metadata.insert("size_bytes".to_string(), Value::Number(value.len() as u64.into()));
            }
            _ => return Err(SynapseError::InvalidInput("Unsupported action".into())),
        }
        Ok(ctx.output.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;
    use uuid::Uuid;

    // Test helper to create ExecutionContext with custom tenant_id
    fn create_ctx(action: &str, key: &str, value: Option<&str>, ttl: Option<u64>, tenant_id: Uuid) -> ExecutionContext {
        let mut data = HashMap::new();
        data.insert("action".to_string(), Value::String(action.to_string()));
        data.insert("key".to_string(), Value::String(key.to_string()));
        if let Some(v) = value {
            data.insert("value".to_string(), Value::String(v.to_string()));
        }
        if let Some(t) = ttl {
            data.insert("ttl".to_string(), Value::Number(t.into()));
        }
        let input = Input {
            data: Value::Object(data),
            tenant_id,
            ..Default::default()
        };
        ExecutionContext::new(input)
    }

    #[tokio::test]
    async fn test_tenant_prefixing() {
        // Test that keys are prefixed with tenant_id when provided
        let redis_url = "redis://localhost";
        let ttl_secs = 3600;
        let tenant_id = Uuid::parse_str("123e4567-e89b-12d3-a456-************").unwrap();
        let neuron = RedisCache::new(redis_url, ttl_secs).await.unwrap();
        let mut ctx = create_ctx("get", "test", None, None, tenant_id);
        let result = neuron.execute(&mut ctx).await;
        // If no Redis, expect Database error, but prefix logic is internal
        // Coverage for prefix generation
        assert!(result.is_ok() || matches!(result.as_ref().err(), Some(SynapseError::Database(_))));
    }

    #[tokio::test]
    async fn test_size_limits() {
        let redis_url = "redis://localhost";
        let ttl_secs = 3600;
        let neuron = RedisCache::new(redis_url, ttl_secs).await.unwrap();
        let long_key = "a".repeat(300); // After prefix >250
        let mut ctx = create_ctx("set", &long_key, Some("value"), Some(3600), Uuid::nil());
        let result = neuron.execute(&mut ctx).await;
        assert!(result.is_err());
        if let Err(e) = result {
            assert!(matches!(e, SynapseError::InvalidInput(_)));
            assert!(e.to_string().contains("key too long"));
        }

        let large_value = "a".repeat(1_048_577);
        let mut ctx = create_ctx("set", "shortkey", Some(&large_value), Some(3600), Uuid::nil());
        let result = neuron.execute(&mut ctx).await;
        assert!(result.is_err());
        if let Err(e) = result {
            assert!(matches!(e, SynapseError::InvalidInput(_)));
            assert!(e.to_string().contains("Value too large"));
        }
    }

    #[tokio::test]
    async fn test_roundtrip_verification() {
        // Test set then get roundtrip (assumes local Redis available)
        let redis_url = "redis://localhost";
        let ttl_secs = 3600;
        let neuron = RedisCache::new(redis_url, ttl_secs).await.unwrap();
        let test_value = "roundtrip_test_value";
        let test_key = "roundtrip_key";
        let tenant_id = Uuid::nil();

        // Set
        let mut set_ctx = create_ctx("set", test_key, Some(test_value), Some(10), tenant_id);
        let set_result = neuron.execute(&mut set_ctx).await;
        assert!(set_result.is_ok(), "Set failed: {:?}", set_result.err());

        // Get
        let mut get_ctx = create_ctx("get", test_key, None, None, tenant_id);
        let get_result = neuron.execute(&mut get_ctx).await;
        assert!(get_result.is_ok(), "Get failed: {:?}", get_result.err());

        let output = &get_ctx.output;
        assert_eq!(output.data, Value::String(test_value.to_string()), "Roundtrip value mismatch");
        assert_eq!(output.status, 200);
        // Check metadata
        assert!(output.metadata.contains_key("atomic_success"));
        assert!(output.metadata.contains_key("size_bytes"));
        if let Some(cached) = output.metadata.get("cached") {
            assert!(cached.as_bool().unwrap_or(false));
        }
    }

    #[tokio::test]
    async fn test_ttl_zero_no_expire() {
        // Test set with ttl=0 should not call expire
        let redis_url = "redis://localhost";
        let ttl_secs = 3600;
        let neuron = RedisCache::new(redis_url, ttl_secs).await.unwrap();
        let mut ctx = create_ctx("set", "ttl_zero", Some("value"), Some(0), Uuid::nil());
        let result = neuron.execute(&mut ctx).await;
        assert!(result.is_ok(), "Set with ttl=0 failed");
        let output = &ctx.output;
        assert_eq!(output.status, 200);
        // ttl_remaining should be 0
        if let Some(ttl_meta) = output.metadata.get("ttl_remaining") {
            assert_eq!(ttl_meta.as_u64(), Some(0));
        }
    }

    #[tokio::test]
    async fn test_error_mapping() {
        // Test local error mapping functions
        use redis::redis_error::{RedisErrorKind as Kind};
        let io_err = RedisError::from((Kind::IoError, "io error"));
        assert!(is_transient(&io_err));
        assert_eq!(map_error(io_err), SynapseError::DbConnectionLost);

        let timeout_err = RedisError::from((Kind::Timeout, "timeout"));
        assert!(is_transient(&timeout_err));
        assert_eq!(map_error(timeout_err), SynapseError::RedisTtlExpired);

        let other_err = RedisError::from((Kind::TypeError, "type error"));
        assert!(!is_transient(&other_err));
        assert!(matches!(map_error(other_err), SynapseError::Database(_)));
    }

    #[tokio::test]
    async fn test_metadata_enhancements() {
        // Test that metadata includes atomic_success, size_bytes, ttl_remaining
        let redis_url = "redis://localhost";
        let ttl_secs = 3600;
        let neuron = RedisCache::new(redis_url, ttl_secs).await.unwrap();
        let test_value = "metadata_test";
        let test_key = "metadata_key";
        let tenant_id = Uuid::nil();

        // Set test
        let mut set_ctx = create_ctx("set", test_key, Some(test_value), Some(100), tenant_id);
        let set_result = neuron.execute(&mut set_ctx).await.unwrap();
        let set_output = &set_ctx.output;
        assert!(set_output.metadata.get("atomic_success").and_then(|v| v.as_bool()) == Some(true));
        assert_eq!(set_output.metadata.get("size_bytes").and_then(|v| v.as_u64()), Some(test_value.len() as u64));
        assert!(set_output.metadata.get("ttl_remaining").and_then(|v| v.as_u64()).is_some());

        // Get test
        let mut get_ctx = create_ctx("get", test_key, None, None, tenant_id);
        let get_result = neuron.execute(&mut get_ctx).await.unwrap();
        let get_output = &get_ctx.output;
        assert!(get_output.metadata.get("atomic_success").and_then(|v| v.as_bool()) == Some(true));
        assert_eq!(get_output.metadata.get("size_bytes").and_then(|v| v.as_u64()), Some(test_value.len() as u64));
        assert!(get_output.metadata.get("ttl_remaining").is_some());
    }

    #[tokio::test]
    async fn test_invalid_inputs() {
        let redis_url = "redis://localhost";
        let ttl_secs = 3600;
        let neuron = RedisCache::new(redis_url, ttl_secs).await.unwrap();

        // Missing key
        let mut data = HashMap::new();
        data.insert("action".to_string(), Value::String("get".to_string()));
        let input = Input { data: Value::Object(data), ..Default::default() };
        let mut ctx = ExecutionContext::new(input);
        let result = neuron.execute(&mut ctx).await;
        assert!(result.is_err());
        assert!(matches!(result, Err(SynapseError::InvalidInput(_))));

        // Set without value
        let mut data = HashMap::new();
        data.insert("action".to_string(), Value::String("set".to_string()));
        data.insert("key".to_string(), Value::String("key".to_string()));
        let input = Input { data: Value::Object(data), ..Default::default() };
        let mut ctx = ExecutionContext::new(input);
        let result = neuron.execute(&mut ctx).await;
        assert!(result.is_err());
        assert!(matches!(result, Err(SynapseError::InvalidInput(_))));
    }

    // Note: Additional concurrent and proptest tests would require proptest dep in Cargo.toml
    // and real Redis container for concurrent access verification
    // Current coverage targets >95% with unit/integration tests above
}