use sqlx::{SqlitePool, Row, Column};
use serde_json::Value;
use async_trait::async_trait;

use crate::traits::Neuron;
use synapse_core::{ExecutionContext, Input, Output, SynapseError};

use uuid::Uuid;

#[derive(Clone)]
pub struct SqliteEmbed {
    pool: SqlitePool,
}

impl SqliteEmbed {
    pub async fn new(database_path: &str) -> Result<Self, SynapseError> {
        let url = format!("sqlite:{}", database_path);
        let pool = sqlx::SqlitePool::connect_lazy(&url)
            .map_err(|e: sqlx::Error| SynapseError::Database(format!("Failed to create SQLite pool: {}", e)))?;

        let instance = Self { pool };

        // Enable WAL mode for better concurrency
        let mut conn = instance.pool.acquire().await
            .map_err(|e| SynapseError::Database(e.to_string()))?;
        sqlx::query("PRAGMA journal_mode = WAL")
            .execute(&mut *conn)
            .await
            .map_err(|e| SynapseError::Database(format!("Failed to enable WAL mode: {}", e)))?;

        // Create users table with tenant isolation if not exists
        sqlx::query(r#"
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tenant_id TEXT NOT NULL,
                name TEXT NOT NULL,
                email TEXT UNIQUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        "#)
        .execute(&mut *conn)
        .await
        .map_err(|e| SynapseError::Database(format!("Failed to create users table: {}", e)))?;

        Ok(instance)
    }
}

#[async_trait]
impl Neuron for SqliteEmbed {
    fn name(&self) -> &'static str {
        "sqlite_embed"
    }

    fn description(&self) -> &'static str {
        "Embedded SQLite database operations with WAL mode and tenant isolation"
    }

    fn version(&self) -> &'static str {
        "1.0.0"
    }

    fn input_schema(&self) -> Option<&'static str> {
        Some(r#"{"type":"object","properties":{"operation":{"type":"string","enum":["query","insert","update","delete"]},"table":{"type":"string"},"query":{"type":"string"},"data":{"type":"object"},"where_clause":{"type":"string"},"tenant_id":{"type":"string"}},"required":["operation"]}"#)
    }

    fn output_schema(&self) -> Option<&'static str> {
        Some(r#"{"type":"object","properties":{"success":{"type":"boolean"},"rows":{"type":"array","items":{"type":"object"}},"affected_rows":{"type":"integer"},"last_insert_id":{"type":"integer"},"error":{"type":["string","null"]}},"required":["success"]}"#)
    }

    async fn execute(&self, ctx: &mut ExecutionContext) -> Result<Output, SynapseError> {
        let operation = ctx.input.data.get("operation")
            .and_then(|v| v.as_str())
            .ok_or(SynapseError::InvalidInput("Missing operation parameter".into()))?;

        let effective_tenant_id = ctx.input.data.get("tenant_id")
            .and_then(|v| v.as_str().map(|s| s.to_string()))
            .or_else(|| if ctx.tenant_id.is_nil() { Some("default".to_string()) } else { Some(ctx.tenant_id.to_string()) });

        let mut output = Output::default();
        let mut output_data = serde_json::Map::new();
        output_data.insert("success".to_string(), Value::Bool(true));
        output_data.insert("error".to_string(), Value::Null);

        match operation {
            "query" => {
                let table = ctx.input.data.get("table")
                    .and_then(|v| v.as_str())
                    .unwrap_or("users");
                let where_clause = ctx.input.data.get("where_clause")
                    .and_then(|v| v.as_str())
                    .unwrap_or("1=1");

                let query = format!("SELECT * FROM {} WHERE tenant_id = ? AND {}", table, where_clause);
                let rows = sqlx::query(&query)
                    .bind(effective_tenant_id.as_ref().unwrap())
                    .fetch_all(&self.pool)
                    .await
                    .map_err(|e| SynapseError::Database(format!("Query failed: {}", e)))?;

                let mut row_values = Vec::new();
                for row in rows {
                    let mut row_map = serde_json::Map::new();
                    let columns: Vec<&str> = row.columns().iter().map(|c| c.name()).collect();
                    for col in columns {
                        if let Ok(value) = row.try_get::<Value, &str>(col) {
                            row_map.insert(col.to_string(), value);
                        }
                    }
                    row_values.push(Value::Object(row_map));
                }
                output_data.insert("rows".to_string(), Value::Array(row_values));
            }

            "insert" => {
                let table = ctx.input.data.get("table")
                    .and_then(|v| v.as_str())
                    .ok_or(SynapseError::InvalidInput("Missing table for insert".into()))?;
                let insert_data = ctx.input.data.get("data")
                    .and_then(|v| v.as_object())
                    .ok_or(SynapseError::InvalidInput("Missing data object for insert".into()))?;

                let columns: Vec<String> = insert_data.keys().cloned().collect();
                let column_names: String = columns.iter().map(|s| s.as_str()).collect::<Vec<_>>().join(", ");
                let placeholders: String = columns.iter().map(|_| "?").collect::<Vec<_>>().join(", ");
                let query = format!("INSERT INTO {} (tenant_id, {}) VALUES (?, {})", table, column_names, placeholders);

                let tenant_ref = effective_tenant_id.as_ref().unwrap();
                let mut query_builder = sqlx::query(&query).bind(tenant_ref);
                for value in insert_data.values() {
                    match value {
                        Value::String(s) => query_builder = query_builder.bind(s),
                        Value::Number(n) => if let Some(i) = n.as_i64() { query_builder = query_builder.bind(i) } else { return Err(SynapseError::InvalidInput("Unsupported number type".into())); },
                        Value::Bool(b) => query_builder = query_builder.bind(b),
                        _ => return Err(SynapseError::InvalidInput("Unsupported data type for insert".into())),
                    }
                }

                let result = query_builder.execute(&self.pool).await
                    .map_err(|e| SynapseError::Database(format!("Insert failed: {}", e)))?;
                output_data.insert("affected_rows".to_string(), Value::Number((result.rows_affected() as u64).into()));
                output_data.insert("last_insert_id".to_string(), Value::Number((result.last_insert_rowid() as u64).into()));
            }

            "update" => {
                let table = ctx.input.data.get("table")
                    .and_then(|v| v.as_str())
                    .ok_or(SynapseError::InvalidInput("Missing table for update".into()))?;
                let update_data = ctx.input.data.get("data")
                    .and_then(|v| v.as_object())
                    .ok_or(SynapseError::InvalidInput("Missing data object for update".into()))?;
                let where_clause = ctx.input.data.get("where_clause")
                    .and_then(|v| v.as_str())
                    .unwrap_or("1=1");

                let set_clause: String = update_data.iter()
                    .map(|(k, _v)| format!("{} = ?", k))
                    .collect::<Vec<_>>().join(", ");
                let query = format!("UPDATE {} SET {}, tenant_id = ? WHERE tenant_id = ? AND {}", table, set_clause, where_clause);

                let tenant_ref = effective_tenant_id.as_ref().unwrap();
                let mut query_builder = sqlx::query(&query).bind(tenant_ref);
                for value in update_data.values() {
                    match value {
                        Value::String(s) => query_builder = query_builder.bind(s),
                        Value::Number(n) => if let Some(i) = n.as_i64() { query_builder = query_builder.bind(i) } else { return Err(SynapseError::InvalidInput("Unsupported number type".into())); },
                        Value::Bool(b) => query_builder = query_builder.bind(b),
                        _ => return Err(SynapseError::InvalidInput("Unsupported data type for update".into())),
                    }
                }
                query_builder = query_builder.bind(tenant_ref);

                let result = query_builder.execute(&self.pool).await
                    .map_err(|e| SynapseError::Database(format!("Update failed: {}", e)))?;
                output_data.insert("affected_rows".to_string(), Value::Number((result.rows_affected() as u64).into()));
            }

            "delete" => {
                let table = ctx.input.data.get("table")
                    .and_then(|v| v.as_str())
                    .ok_or(SynapseError::InvalidInput("Missing table for delete".into()))?;
                let where_clause = ctx.input.data.get("where_clause")
                    .and_then(|v| v.as_str())
                    .unwrap_or("1=1");

                let query = format!("DELETE FROM {} WHERE tenant_id = ? AND {}", table, where_clause);
                let result = sqlx::query(&query)
                    .bind(effective_tenant_id.as_ref().unwrap())
                    .execute(&self.pool)
                    .await
                    .map_err(|e| SynapseError::Database(format!("Delete failed: {}", e)))?;
                output_data.insert("affected_rows".to_string(), Value::Number((result.rows_affected() as u64).into()));
            }

            _ => {
                output_data.insert("success".to_string(), Value::Bool(false));
                output_data.insert("error".to_string(), Value::String(format!("Unsupported operation: {}", operation)));
                output.status = 400;
                output.data = Value::Object(output_data);
                return Ok(output);
            }
        }

        output.status = 200;
        output.data = Value::Object(output_data);
        Ok(output)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::Value;

    #[tokio::test]
    async fn test_sqlite_query() {
        let temp_file = tempfile::NamedTempFile::new().map_err(|_| SynapseError::Database("Temp file creation failed".into())).expect("Temp file creation failed");
        let db_path = temp_file.path().to_str().unwrap();

        if let Ok(neuron) = SqliteEmbed::new(db_path).await {
            // Insert test data first
            let input_data = vec![
                ("operation".to_string(), Value::String("insert".to_string())),
                ("table".to_string(), Value::String("users".to_string())),
                ("data".to_string(), serde_json::json!({ "name": "Test User", "email": "<EMAIL>" })),
                ("tenant_id".to_string(), Value::String("tenant1".to_string())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input_insert = Input::default();
            let mut ctx_insert = ExecutionContext::new(input_insert);
            ctx_insert.input.data = input_data;
            let _ = neuron.execute(&mut ctx_insert).await;

            // Now query
            let input_data = vec![
                ("operation".to_string(), Value::String("query".to_string())),
                ("table".to_string(), Value::String("users".to_string())),
                ("tenant_id".to_string(), Value::String("tenant1".to_string())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input_query = Input::default();
            let mut ctx = ExecutionContext::new(input_query);
            ctx.input.data = input_data;
            ctx.tenant_id = Uuid::new_v4();

            match neuron.execute(&mut ctx).await {
                Ok(output) => {
                    let rows = output.data.get("rows").and_then(|v| v.as_array());
                    assert!(rows.is_some());
                    assert!(!rows.unwrap().is_empty());
                }
                Err(_) => panic!("Query should succeed"),
            }
        }
    }

    #[tokio::test]
    async fn test_sqlite_insert_user() {
        let temp_file = tempfile::NamedTempFile::new().map_err(|_| SynapseError::Database("Temp file creation failed".into())).expect("Temp file creation failed");
        let db_path = temp_file.path().to_str().unwrap();

        if let Ok(neuron) = SqliteEmbed::new(db_path).await {
            let tenant_id = Uuid::new_v4().to_string();
            let input_data = vec![
                ("operation".to_string(), Value::String("insert".to_string())),
                ("table".to_string(), Value::String("users".to_string())),
                ("data".to_string(), serde_json::json!({ "name": "Alice", "email": "<EMAIL>" })),
                ("tenant_id".to_string(), Value::String(tenant_id.clone())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input = Input::default();
            let mut ctx = ExecutionContext::new(input);
            ctx.input.data = input_data;

            match neuron.execute(&mut ctx).await {
                Ok(output) => {
                    let success = output.data.get("success").and_then(|v| v.as_bool()).unwrap_or(false);
                    let affected = output.data.get("affected_rows").and_then(|v| v.as_u64()).unwrap_or(0);
                    assert!(success);
                    assert_eq!(affected, 1);
                }
                Err(e) => panic!("Insert failed: {}", e),
            }
        }
    }

    #[tokio::test]
    async fn test_sqlite_update_user() {
        let temp_file = tempfile::NamedTempFile::new().map_err(|_| SynapseError::Database("Temp file creation failed".into())).expect("Temp file creation failed");
        let db_path = temp_file.path().to_str().unwrap();

        if let Ok(neuron) = SqliteEmbed::new(db_path).await {
            // Insert first
            let tenant_id = Uuid::new_v4().to_string();
            let insert_data = vec![
                ("operation".to_string(), Value::String("insert".to_string())),
                ("table".to_string(), Value::String("users".to_string())),
                ("data".to_string(), serde_json::json!({ "name": "Bob", "email": "<EMAIL>" })),
                ("tenant_id".to_string(), Value::String(tenant_id.clone())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input_insert = Input::default();
            let mut ctx_insert = ExecutionContext::new(input_insert);
            ctx_insert.input.data = insert_data;
            let _ = neuron.execute(&mut ctx_insert).await;

            // Update
            let update_data = vec![
                ("operation".to_string(), Value::String("update".to_string())),
                ("table".to_string(), Value::String("users".to_string())),
                ("data".to_string(), serde_json::json!({ "name": "Bob Updated" })),
                ("where_clause".to_string(), Value::String("email = '<EMAIL>'".to_string())),
                ("tenant_id".to_string(), Value::String(tenant_id.clone())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input_update = Input::default();
            let mut ctx = ExecutionContext::new(input_update);
            ctx.input.data = update_data;

            match neuron.execute(&mut ctx).await {
                Ok(output) => {
                    let success = output.data.get("success").and_then(|v| v.as_bool()).unwrap_or(false);
                    let affected = output.data.get("affected_rows").and_then(|v| v.as_u64()).unwrap_or(0);
                    assert!(success);
                    assert_eq!(affected, 1);
                }
                Err(e) => panic!("Update failed: {}", e),
            }
        }
    }

    #[tokio::test]
    async fn test_sqlite_delete_user() {
        let temp_file = tempfile::NamedTempFile::new().map_err(|_| SynapseError::Database("Temp file creation failed".into())).expect("Temp file creation failed");
        let db_path = temp_file.path().to_str().unwrap();

        if let Ok(neuron) = SqliteEmbed::new(db_path).await {
            // Insert first
            let tenant_id = Uuid::new_v4().to_string();
            let insert_data = vec![
                ("operation".to_string(), Value::String("insert".to_string())),
                ("table".to_string(), Value::String("users".to_string())),
                ("data".to_string(), serde_json::json!({ "name": "Charlie", "email": "<EMAIL>" })),
                ("tenant_id".to_string(), Value::String(tenant_id.clone())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input_insert = Input::default();
            let mut ctx_insert = ExecutionContext::new(input_insert);
            ctx_insert.input.data = insert_data;
            let _ = neuron.execute(&mut ctx_insert).await;

            // Delete
            let delete_data = vec![
                ("operation".to_string(), Value::String("delete".to_string())),
                ("table".to_string(), Value::String("users".to_string())),
                ("where_clause".to_string(), Value::String("email = '<EMAIL>'".to_string())),
                ("tenant_id".to_string(), Value::String(tenant_id.clone())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input_delete = Input::default();
            let mut ctx = ExecutionContext::new(input_delete);
            ctx.input.data = delete_data;

            match neuron.execute(&mut ctx).await {
                Ok(output) => {
                    let success = output.data.get("success").and_then(|v| v.as_bool()).unwrap_or(false);
                    let affected = output.data.get("affected_rows").and_then(|v| v.as_u64()).unwrap_or(0);
                    assert!(success);
                    assert_eq!(affected, 1);
                }
                Err(e) => panic!("Delete failed: {}", e),
            }
        }
    }

    #[tokio::test]
    async fn test_sqlite_tenant_isolation() {
        let temp_file = tempfile::NamedTempFile::new().map_err(|_| SynapseError::Database("Temp file creation failed".into())).expect("Temp file creation failed");
        let db_path = temp_file.path().to_str().unwrap();

        if let Ok(neuron) = SqliteEmbed::new(db_path).await {
            // Insert for tenant1
            let tenant1_id = "tenant1-uuid-123".to_string();
            let input_data1 = vec![
                ("operation".to_string(), Value::String("insert".to_string())),
                ("table".to_string(), Value::String("users".to_string())),
                ("data".to_string(), serde_json::json!({ "name": "Tenant1 User", "email": "<EMAIL>" })),
                ("tenant_id".to_string(), Value::String(tenant1_id.clone())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input1 = Input::default();
            let mut ctx1 = ExecutionContext::new(input1);
            ctx1.input.data = input_data1;
            let _ = neuron.execute(&mut ctx1).await;

            // Insert for tenant2
            let tenant2_id = "tenant2-uuid-456".to_string();
            let input_data2 = vec![
                ("operation".to_string(), Value::String("insert".to_string())),
                ("table".to_string(), Value::String("users".to_string())),
                ("data".to_string(), serde_json::json!({ "name": "Tenant2 User", "email": "<EMAIL>" })),
                ("tenant_id".to_string(), Value::String(tenant2_id.clone())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input2 = Input::default();
            let mut ctx2 = ExecutionContext::new(input2);
            ctx2.input.data = input_data2;
            let _ = neuron.execute(&mut ctx2).await;

            // Query for tenant1 - should only see tenant1 data
            let query_data = vec![
                ("operation".to_string(), Value::String("query".to_string())),
                ("table".to_string(), Value::String("users".to_string())),
                ("tenant_id".to_string(), Value::String(tenant1_id.clone())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input_query = Input::default();
            let mut ctx_query = ExecutionContext::new(input_query);
            ctx_query.input.data = query_data;

            match neuron.execute(&mut ctx_query).await {
                Ok(output) => {
                    let rows = output.data.get("rows").and_then(|v| v.as_array()).unwrap();
                    assert_eq!(rows.len(), 1);
                    assert!(rows[0].get("tenant_id").and_then(|v| v.as_str()).unwrap_or("") == tenant1_id);
                }
                Err(e) => panic!("Tenant query failed: {}", e),
            }
        }
    }

    #[tokio::test]
    async fn test_sqlite_connection_error() {
        // Use invalid path to trigger connection error
        match SqliteEmbed::new("invalid:path").await {
            Ok(_) => panic!("Should fail to connect"),
            Err(e) => {
                assert!(matches!(e, SynapseError::Database(_)));
                assert!(e.to_string().contains("unable to open database file"));
            }
        }
    }

    #[tokio::test]
    async fn test_sqlite_invalid_operation() {
        let temp_file = tempfile::NamedTempFile::new().map_err(|_| SynapseError::Database("Temp file creation failed".into())).expect("Temp file creation failed");
        let db_path = temp_file.path().to_str().unwrap();

        if let Ok(neuron) = SqliteEmbed::new(db_path).await {
            let input_data = vec![
                ("operation".to_string(), Value::String("invalid_op".to_string())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input = Input::default();
            let mut ctx = ExecutionContext::new(input);
            ctx.input.data = input_data;

            match neuron.execute(&mut ctx).await {
                Ok(output) => {
                    let success = output.data.get("success").and_then(|v| v.as_bool()).unwrap_or(true);
                    let error = output.data.get("error").and_then(|v| v.as_str());
                    assert!(!success);
                    assert!(error.is_some());
                    assert!(error.unwrap().contains("Unsupported operation"));
                    assert_eq!(output.status, 400);
                }
                Err(_) => panic!("Invalid operation should return error output, not propagate"),
            }
        }
    }

    #[tokio::test]
    async fn test_sqlite_missing_table() {
        let temp_file = tempfile::NamedTempFile::new().map_err(|_| SynapseError::Database("Temp file creation failed".into())).expect("Temp file creation failed");
        let db_path = temp_file.path().to_str().unwrap();

        if let Ok(neuron) = SqliteEmbed::new(db_path).await {
            let input_data = vec![
                ("operation".to_string(), Value::String("query".to_string())),
                ("table".to_string(), Value::String("nonexistent".to_string())),
                ("tenant_id".to_string(), Value::String(Uuid::new_v4().to_string())),
            ].iter().map(|(k,v)| (k.clone(), v.clone())).collect();
            let input = Input::default();
            let mut ctx = ExecutionContext::new(input);
            ctx.input.data = input_data;

            match neuron.execute(&mut ctx).await {
                Ok(output) => {
                    // Should handle gracefully with empty rows
                    let success = output.data.get("success").and_then(|v| v.as_bool()).unwrap_or(false);
                    let empty_rows = Vec::new();
                    let rows = output.data.get("rows").and_then(|v| v.as_array()).unwrap_or(&empty_rows);
                    assert!(success);
                    assert_eq!(rows.len(), 0);
                }
                Err(e) if matches!(e, SynapseError::Database(_)) => {
                    // Handle SQL error for missing table as empty result
                    assert!(true);
                }
                Err(e) => panic!("Unexpected error for missing table: {}", e),
            }
        }
    }
}