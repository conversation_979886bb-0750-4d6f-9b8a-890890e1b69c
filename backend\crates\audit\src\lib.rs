//! Audit crate for Synapse-IO: SHA256 audit trails, compliance logging, security events

pub mod audit;
pub mod events;
pub mod logging;
pub mod compliance;

pub use audit::{AuditLogger, AuditRecord};
pub use events::{SecurityEvent, AuditEvent};
pub use logging::{ComplianceLogger, log_compliance_event};
pub use compliance::{ComplianceReport, generate_report};

use synapse_core::SynapseError;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_audit_imports() {
        // Basic import test for audit module
        let _audit_logger = AuditLogger::new();
        let _record = AuditRecord::default();
        let _security_event = SecurityEvent::default();
        let _compliance_report = ComplianceReport::default();
    }
}