# Synapse-IO Security & Auth Planning

## Introduction

Security and authentication are foundational to Synapse-IO, ensuring the protection of user pathways, sensitive data, and intellectual property while enabling monetization tiers and regulatory compliance. The system must safeguard against unauthorized access, data breaches, and abuse, supporting a closed-source enterprise model that retains competitive advantages.

### Tech Stack
- **Backend**: jsonwebtoken 9 for JWT handling, tower 0.4 and tower-http 0.5 for middleware layers, rustls 0.22 for TLS encryption, pgp for database field encryption.
- **Frontend**: httpOnly cookies for secure token proxying, Zod 3 for input validation.
- **Secrets Management**: dotenvy and env for development, vault-rs integration for production.
- **Compliance**: tracing 0.1 for structured audit logging, sha2 for tamper-proof hashing.

### Design Principles
- **Defense-in-Depth**: Multiple layered protections including middleware guards, encryption, and validation.
- **Least Privilege Roles**: Granular access based on user tiers (free, pro, enterprise).
- **Zero-Trust Tenant Isolation**: All requests validated for tenant context, enforced via RLS and per-tenant limits.
- **Automated Rotation and Testing**: Scheduled key rotations with zero-downtime mechanisms and continuous penetration testing.

## System Architecture

The architecture employs a layered approach: authentication middleware validates tokens, rate and anomaly guards protect against abuse, encryption secures data in transit and at rest, and an audit chain ensures compliance traceability.

### High-Level Layers
- **Auth Middleware**: JWT validation with claims extraction (user_id, tier, tenant_id, exp).
- **Rate/Anomaly Guards**: Per-IP/tenant/user limits and ML-based detection.
- **Encryption Wrappers**: TLS for transit, PGP for sensitive DB fields.
- **Audit Chain**: Immutable logging with SHA256 hashing for tamper-proof retention.

### Mermaid Auth Flow Diagram

```mermaid
sequenceDiagram
    participant F as Frontend
    participant A as API /auth/login
    participant M as Middleware (JWT Validate)
    participant R as Role/Tenant Check
    participant G as Rate/Anomaly Guards
    participant P as Pathway Access
    participant L as Audit Log

    F->>A: User Login POST (credentials)
    A->>A: Mint JWT with claims {user_id, tier, tenant_id, exp}
    A->>F: Response with httpOnly cookie
    F->>M: Request with Bearer Token (from cookie proxy)
    M->>M: Validate token (decode, verify exp/issuer/audience)
    alt Valid Token
        M->>R: Pass claims
        R->>R: Check role (free: limited, enterprise: unlimited) & tenant_id
        alt Authorized
            R->>G: Proceed
            G->>G: Apply rate limits & anomaly detection
            alt No Abuse
                G->>P: Execute pathway
                P->>L: Log action (sha256 chain)
                P->>F: Secure response
            end
        end
    end
```

## Auth & Authorization Details

### JWT
- **Algorithm**: HS256 for access tokens, HS512 for refresh tokens; keys rotated every 90 days.
- **Claims Validation**: Expiration (exp), issuer, audience; custom claims include user_id, tier (free/pro/enterprise), tenant_id.
- **Token Lifecycle**: Short-lived access tokens (15-60 min), refresh tokens for renewal without re-authentication.

### OAuth2
- **Flow**: PKCE for frontend integrations to prevent authorization code interception.
- **Provider Configs**: Handled in neurons (oauth2_flow), with secure redirects to authorized URIs.
- **Implementation**: Neuron-based execution for token exchange and validation.

### SSO
- **Protocols**: SAML and OIDC, configured via sso_redirect neuron with metadata endpoints.
- **Provisioning**: Just-in-time user creation with tenant mapping and role assignment.

### Roles
- **Free Tier**: Limited to 10 pathways/month, basic neurons only.
- **Pro Tier**: Unlimited pathways, access to advanced AI neurons.
- **Enterprise Tier**: Unlimited access, dedicated hosting, SLAs, and full compliance features.

### Tenant Isolation
- **Database**: RLS policies enforcing tenant_id = current_user_tenant().
- **Limits**: Per-tenant rate limiting and separate audit log partitions.
- **Zero-Trust**: Every request re-validates tenant context in middleware.

## Security Layers

### Input Validation
- **Mechanism**: tower-http validate::Request with serde and validator schemas.
- **Constraints**: Request size limited to 1MB, GraphJSON payloads sanitized for injections.

### Rate Limiting
- **Implementation**: tower-limit layer, configured per IP, tenant, and user.
- **Parameters**: Burst allowance of 100 req/s, sustained rate of 10 req/s.

### Anomaly Detection
- **Approach**: Rule-based thresholds combined with ML models in ai crate, trained on audit data.
- **Triggers**: Unusual patterns like excessive neuron usage or login failures; alerts and auto-throttling.

### Encryption
- **Transit**: rustls TLS 1.3 enforced in Axum server for all connections.
- **At-Rest**: PGP encryption for sensitive DB fields (e.g., API keys, PII).
- **Closed-Source**: cargo-obfuscate in build.rs to protect binary IP, no exposed internal APIs.

### Secrets Management
- **Development**: .env files loaded via dotenvy.
- **Production**: vault-rs for secure storage and retrieval; monthly rotation scripts with zero-downtime dual-key support.
- **Policy**: No hard-coded secrets; all loaded dynamically.

### Mermaid Encryption Layers Diagram

```mermaid
sequenceDiagram
    participant F as Frontend Request
    participant T as TLS rustls
    participant V as Validate Zod
    participant R as Rate tower
    participant A as Auth JWT
    participant P as Pathway Execute
    participant U as Audit sha256
    participant S as TLS Response

    F->>T: Encrypt request (TLS 1.3)
    T->>V: Decrypt & validate inputs (Zod schemas)
    V->>R: Apply rate limits (tower-limit)
    R->>A: Validate JWT claims
    A->>P: Execute pathway (tenant-isolated)
    P->>U: Log audit event (sha256 chain append)
    U->>S: Encrypt response (TLS 1.3)
    S->>F: Secure delivery
```

## Compliance & Audit

### HIPAA/SOC2 Requirements
- **Logging**: tracing spans for all requests, masking PII (e.g., hash emails).
- **Tamper-Proof**: SHA256 chaining where each log appends a hash of the previous chain.
- **Retention**: 7-year storage in S3 cold tier; automated cleanup jobs.

### RLS
- **Policies**: Database-level enforcement (e.g., ALTER POLICY tenant_isolation ON pathways USING (tenant_id = current_setting('app.current_tenant'))).
- **Middleware**: Set session config for tenant_id during JWT validation.

### Penetration Testing
- **Tools**: OWASP ZAP blueprints for automated scans targeting auth bypass, injection, and XSS.
- **Frequency**: Quarterly with detailed reports; include auth flow simulations.

### Mermaid Compliance Audit Chain Diagram

```mermaid
graph TD
    A[Request Event] --> B[Capture Span tracing]
    B --> C[Mask PII hash]
    C --> D[Append SHA256 Chain]
    D --> E[Store in Audit DB]
    E --> F[Retention Policy 7y]
    F --> G[S3 Cold Storage]
    G --> H[RBAC Access Logs]
    H --> I[Tamper Check on Query]
    style D fill:#f9f,stroke:#333,stroke-width:2px
    style I fill:#f9f,stroke:#333,stroke-width:2px
```

## Integration Points

### Frontend
- **Login Flow**: authService.ts POST to /auth/login sets httpOnly cookie via backend proxy.
- **Services**: axios interceptors in pathwayService.ts add Authorization from cookie; websocketService.ts uses token query param for secure upgrades.
- **Guards**: Protected routes in App.tsx with auth checks; all inputs validated via Zod.

### Backend
- **Middleware Stack**: In api/main.rs: TraceLayer -> JwtLayer -> RateLayer -> ValidateLayer -> AnomalyLayer -> RouteHandler.
- **Neurons**: Auth neurons (jwt_validate, oauth2_flow) invoked in pathways with secure decoding.
- **WebSockets**: tungstenite upgrades post-JWT check, maintaining tenant context.

### Testing
- **Auth Tests**: Unit/integration with test keys and mock users in CI; cover 95% of flows.
- **E2E**: Cypress for login -> pathway execution -> audit verification.
- **Penetration**: Mock attacks in tests (e.g., invalid tokens, rate bursts) without external mocks.

## File-by-File Plans

### Backend
- **/crates/api/src/middleware.rs**: 
  ```rust
  pub struct JwtMiddleware {
      secret: String,
  }
  impl Layer for JwtMiddleware {
      type Service = JwtService<Self::Service>;
      fn layer(&self, inner: S) -> Self::Service {
          JwtService { inner, secret: Arc::new(self.secret.clone()) }
      }
  }
  ```
  Implements jsonwebtoken decode/verify for claims extraction.

- **/crates/security/src/secrets.rs**:
  ```rust
  pub fn load_key_rotation() -> Result<Vec<u8>, SecretError> {
      dotenvy::var("JWT_SECRET").map(|s| s.as_bytes().to_vec())
          .or_else(|_| vault::get("synapse/jwt-secret"))
  }
  ```
  Includes cron script for monthly rotation.

- **/crates/auth_neurons/src/jwt.rs**:
  ```rust
  struct JwtNeuron { secret: Arc<String> }
  impl Neuron for JwtNeuron {
      async fn execute(&self, input: Input) -> Result<Output, SynapseError> {
          let token = input.headers.get("Authorization").ok_or(SynapseError::MissingToken)?;
          let validation = jsonwebtoken::Validation::new(Algorithm::HS256);
          let data = jsonwebtoken::decode::<Claims>(token, &self.secret, &validation)?;
          Ok(Output::new(data.claims))
      }
  }
  ```
  Tests achieve 95% coverage with valid/invalid token scenarios.

- **/crates/compliance/src/audit.rs**:
  ```rust
  use tracing::{info_span, Span};
  pub fn audit_request(req: &Request, user_id: Uuid) {
      let span = info_span!("request", user_id, method = %req.method(), path = %req.uri().path());
      span.in_scope(|| {
          // Log masked PII, append to sha256 chain
      });
  }
  ```
  Includes retention cleanup job for 7-year policy.

### Frontend
- **src/services/authService.ts**:
  ```typescript
  async login(creds: LoginForm): Promise<void> {
      await axios.post('/auth/login', creds, { withCredentials: true });
      // Backend sets httpOnly cookie
  }
  ```
  Ensures secure: true for WebSocket connections.

- **tests/auth.test.tsx**: RTL tests simulate login, assert cookie set; E2E Cypress covers full login flow.

## Risks & Gaps

### Addressed Risks
- **Key Rotation**: Zero-downtime via dual-key support in middleware (graceful fallback during transition).
- **Anomaly False Positives**: ML feedback loop tuned on audit data, with configurable thresholds and manual override.
- **Penetration**: Quarterly OWASP ZAP scans with automated reports and remediation tracking.

### Gaps
- **Quantum-Resistant Crypto**: Current setup uses HS256/HS512; future migration to post-quantum algorithms (e.g., Kyber) recommended for long-term DB encryption.
- **Zero-Knowledge Proofs**: Potential enhancement for tenant data privacy, allowing computations without exposing raw pathways (investigate zk-SNARKs integration).