//! Security crate for Synapse-IO: Tower JWT RS256, PGP encryption, anomaly detection

pub mod auth;
pub mod encryption;
pub mod anomaly;
pub mod middleware;

pub use auth::{JwtValidator, RS256KeyPair};
pub use encryption::{PgpEncryptor, PgpDecryptor};
pub use anomaly::{AnomalyDetector, RiskScore};
pub use middleware::{security_middleware, rate_limit_middleware};

use synapse_core::SynapseError;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_security_imports() {
        // Basic import test for security module
        let _jwt = JwtValidator::new();
        let _rs256 = RS256KeyPair::generate();
        let _pgp_encrypt = PgpEncryptor::new();
        let _pgp_decrypt = PgpDecryptor::new();
        let _anomaly = AnomalyDetector::default();
    }
}