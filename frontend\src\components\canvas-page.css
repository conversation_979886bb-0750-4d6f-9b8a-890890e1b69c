.react-flow__node {
  cursor: grab;
}

.react-flow__node.react-flow__node-input {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.react-flow__node.react-flow__node-neuron {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.react-flow__edge-path {
  stroke: #64748b;
  stroke-width: 2;
}

.react-flow__handle {
  background: #4facfe;
  border: 2px solid #fff;
}

.react-flow__controls {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.react-flow__minimap {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
}

.react-flow__minimap-mask {
  fill: #4facfe;
}

.react-flow__minimap-node {
  fill: #667eea;
  stroke: #fff;
  stroke-width: 2;
}