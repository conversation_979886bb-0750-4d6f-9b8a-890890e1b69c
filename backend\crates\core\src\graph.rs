use std::collections::{HashMap, VecDeque};
use std::time::Duration;
use tokio::time::timeout;
use uuid::Uuid as NodeId;
use crate::context::{ExecutionContext, Input, Output};
use crate::error::SynapseError;
// use crate::neurons::Neuron; // Temporary comment until neurons crate is implemented
use serde::{Serialize, Deserialize};

#[derive(Clone, Serialize, Deserialize)]
pub struct Graph {
    // pub nodes: HashMap<NodeId, Box<dyn Neuron + Send + Sync>>, // Temporary comment
    pub nodes: HashMap<NodeId, String>, // Temporary placeholder type
    pub edges: Vec<(NodeId, NodeId)>,
    pub start_node: NodeId,
}

impl Graph {
    pub fn new(start: NodeId) -> Self {
        Self {
            nodes: HashMap::new(),
            edges: Vec::new(),
            start_node: start
        }
    }

    pub fn add_node(&mut self, id: NodeId /* , neuron: Box<dyn Neuron + Send + Sync> */) { // Temporary comment
        // self.nodes.insert(id, neuron); // Temporary comment
        self.nodes.insert(id, "placeholder".to_string()); // Temporary
    }

    pub fn add_edge(&mut self, from: NodeId, to: NodeId) {
        self.edges.push((from, to));
    }

    fn topo_sort(&self, start: NodeId) -> Result<Vec<NodeId>, SynapseError> {
        // Kahn's algorithm implementation with cycle detection
        let mut indegree = HashMap::new();
        for node in self.nodes.keys() {
            indegree.insert(*node, 0);
        }
        for &(from, to) in &self.edges {
            if let Some(count) = indegree.get_mut(&to) {
                *count += 1;
            }
        }

        let mut queue = VecDeque::new();
        if let Some(&count) = indegree.get(&start) {
            if count == 0 {
                queue.push_back(start);
            }
        }

        let mut order = Vec::new();
        while let Some(node) = queue.pop_front() {
            order.push(node);
            for &(from, to) in &self.edges {
                if from == node {
                    if let Some(count) = indegree.get_mut(&to) {
                        *count -= 1;
                        if *count == 0 {
                            queue.push_back(to);
                        }
                    }
                }
            }
        }

        if order.len() != self.nodes.len() {
            return Err(SynapseError::GraphCycle);
        }

        Ok(order)
    }

    pub async fn execute(&self, input: Input) -> Result<Output, SynapseError> {
        let mut ctx = ExecutionContext::new(input);
        let order = self.topo_sort(self.start_node)?;
        for node_id in order {
            // if let Some(neuron) = self.nodes.get(&node_id) { // Temporary comment
            //     let out = timeout(Duration::from_secs(30), neuron.execute(&mut ctx)).await
            //         .map_err(|_| SynapseError::Timeout)??;
            //     ctx.update_output(out);
            // }
            // Temporary: skip node execution
        }
        Ok(ctx.output)
    }

    // Parallel execution with tokio::select! for independent branches
    pub async fn execute_parallel(&self, input: Input) -> Result<Output, SynapseError> {
        self.execute(input).await // Temporary: use sequential for now
    }
}