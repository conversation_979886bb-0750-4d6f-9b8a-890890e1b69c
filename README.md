# Synapse-IO

Synapse-IO is an enterprise-grade visual API programming platform featuring Synapse Studio (React/TS Vite PWA) for drag-and-drop pathway design with 74+ neurons, Rust backend with Cargo workspace crates for core execution, neurons, API, DB, AI, marketplace, security, versioning, scalability. Includes AI features like Impulse NL-to-graph, full auth/compliance, deployment to K8s, and monetization via SaaS tiers.

## Setup

### Backend
```bash
cd backend
cargo build
```

### Frontend
```bash
cd frontend
npm install
npm run dev
```

## Architecture
- **Core**: Tokio-based DAG graph execution
- **Neurons**: 74+ modular implementations (OAuth, DB, AI proxies, etc.)
- **API**: Axum routes with Tower middleware, WebSockets
- **DB**: SQLx PostgreSQL with RLS, Redis
- **AI**: Proxies to OpenAI/Claude/Grok with chain-of-thought
- **Frontend**: ReactFlow canvas, Zustand stores, real-time collab
- **Security**: JWT RS256, PGP, anomaly detection
- **Deployment**: <PERSON>er, K8s ArgoCD, cross-compiled .exe

See docs/ for detailed plans.