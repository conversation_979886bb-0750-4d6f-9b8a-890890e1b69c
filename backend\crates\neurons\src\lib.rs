//! Neurons crate for Synapse-IO: Modular processing units for the graph

pub mod traits;
pub mod auth;
pub mod db;

pub use crate::traits::Neuron;
pub use auth::*;
pub use db::*;

#[cfg(test)]
mod tests {
    use crate::Neuron;
    use synapse_core::{ExecutionContext, Input, Output, SynapseError};
    use async_trait::async_trait;
    use std::collections::HashMap;
    use serde_json::Value;
    use chrono;
    use uuid::Uuid;

    #[derive(Clone)]
    struct TestNeuron;

    #[async_trait]
    impl Neuron for TestNeuron {
        fn name(&self) -> &'static str {
            "test_neuron"
        }

        fn description(&self) -> &'static str {
            "A test neuron for validation"
        }

        fn version(&self) -> &'static str {
            "1.0.0"
        }

        fn input_schema(&self) -> Option<&'static str> {
            Some(r#"{"type":"object","properties":{"test_input":{"type":"string","description":"Test input string"}},"required":["test_input"]}"#)
        }

        fn output_schema(&self) -> Option<&'static str> {
            Some(r#"{"type":"object","properties":{"processed":{"type":"string","description":"Processed test output"},"timestamp":{"type":"string","format":"date-time"}},"required":["processed","timestamp"]}"#)
        }

        async fn execute(&self, _ctx: &mut ExecutionContext) -> Result<Output, SynapseError> {
            let output_data = serde_json::json!({
                "processed": "Processed: test",
                "timestamp": chrono::Utc::now().to_rfc3339()
            });

            let mut output = Output::default();
            output.data = output_data;
            output.status = 200;

            Ok(output)
        }
    }

    #[tokio::test]
    async fn test_neuron_execution() {
        let neuron = TestNeuron;
        let input = Input::default();
        let mut ctx = ExecutionContext::new(input);
        let result = neuron.execute(&mut ctx).await;

        assert!(result.is_ok());
        
        let output = result.unwrap();
        assert_eq!(output.status, 200);
        assert!(output.data.is_object());
        assert!(output.data.get("processed").is_some());
        assert!(output.data.get("timestamp").is_some());
    }

    #[test]
    fn test_neuron_metadata() {
        let neuron = TestNeuron;
        
        assert_eq!(neuron.name(), "test_neuron");
        assert_eq!(neuron.description(), "A test neuron for validation");
        assert_eq!(neuron.version(), "1.0.0");
        
        if let Some(input_schema) = neuron.input_schema() {
            assert!(input_schema.contains("test_input"));
            assert!(input_schema.contains("required"));
        }
        
        if let Some(output_schema) = neuron.output_schema() {
            assert!(output_schema.contains("processed"));
            assert!(output_schema.contains("timestamp"));
        }
    }
}